// <copyright file="SafeMeteringOptionValidatorNoLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using Microsoft.R9.Extensions.Metering;
using Microsoft.R9.Extensions.Metering.Exporters;

namespace Microsoft.M365.Core.Telemetry.R9.Metering.Substrate.Internal
{
    /// <summary>
    /// Safe validation helper that doesn't throw exceptions or use ILogger
    /// </summary>
    internal static class SafeValidatorHelper
    {
        /// <summary>
        /// Mapping between option type and configuration section name
        /// </summary>
        private static readonly Dictionary<Type, string> configSectionName = new ()
        {
            [typeof(MeteringOptions)] = "SubstrateMetering:R9Metering",
            [typeof(GenevaMeteringExporterOptions)] = "SubstrateMetering:GenevaExporter"
        };

        /// <summary>
        /// Safely update options from configuration (general for all options)
        /// </summary>
        /// <typeparam name="T">option type</typeparam>
        /// <param name="options">The options to update</param>
        /// <param name="configuration">The configuration</param>
        /// <returns>Validation result</returns>
        internal static ValidationResult SafeUpdateOption<T>(this T options, IConfiguration configuration)
        {
            try
            {
                if (configSectionName.TryGetValue(typeof(T), out var sectionName))
                {
                    var section = configuration.GetSection(sectionName);
                    if (section.Exists())
                    {
                        section.Bind(options);
                        return ValidationResult.Success();
                    }
                    else
                    {
                        // Configuration section not found, log and use defaults
                        SubstrateMeteringEventSource.Log.ConfigurationSectionNotFound(sectionName, typeof(T).Name);
                        return ValidationResult.Success();
                    }
                }
                else
                {
                    var error = $"Option Type [{typeof(T)}] is not registered for configuration loading.";
                    SubstrateMeteringEventSource.Log.ConfigurationBindingFailed(typeof(T).Name, error);
                    return ValidationResult.Failure(error);
                }
            }
            catch (Exception ex)
            {
                var error = $"Failed to bind configuration for {typeof(T).Name}: {ex.Message}";
                SubstrateMeteringEventSource.Log.ConfigurationBindingFailed(typeof(T).Name, error);
                return ValidationResult.Failure(error);
            }
        }
    }

    /// <summary>
    /// Safe validation for metering options without ILogger dependency
    /// </summary>
    public static class SafeMeteringOptionValidator
    {
        /// <summary>
        /// Safely load values from configuration section "Metering", update and validate metering options
        /// </summary>
        /// <param name="options">The metering options</param>
        /// <param name="configuration">The configuration</param>
        /// <returns>Validation result</returns>
        public static ValidationResult SafeUpdateAndValidateOptions(this MeteringOptions options, IConfiguration configuration)
        {
            var updateResult = options.SafeUpdateOption(configuration);
            if (!updateResult.IsSuccess)
            {
                return updateResult;
            }

            // Set default state
            options.MeterState = MeteringState.Disabled;

            // Validate MeterStateOverrides
            if (options.MeterStateOverrides == null || options.MeterStateOverrides.Count == 0)
            {
                SubstrateMeteringEventSource.Log.MeterStateOverridesEmpty("Disabled");
                // Apply safe defaults instead of failing
                ApplyDefaultMeterStateOverrides(options);
            }

            return ValidationResult.Success();
        }

        /// <summary>
        /// Applies default meter state overrides when configuration is missing
        /// </summary>
        /// <param name="options">The metering options</param>
        private static void ApplyDefaultMeterStateOverrides(MeteringOptions options)
        {
            if (options.MeterStateOverrides == null)
            {
                options.MeterStateOverrides = new Dictionary<string, MeteringState>();
            }

            // Add a default disabled state for all meters
            if (options.MeterStateOverrides.Count == 0)
            {
                options.MeterStateOverrides["*"] = MeteringState.Disabled;
            }
        }
    }

    /// <summary>
    /// Safe validation for Geneva metering exporter options without ILogger dependency
    /// </summary>
    public static class SafeGenevaMeteringOptionValidator
    {
        /// <summary>
        /// Safely load values from configuration "GenevaMeteringExporter", update and validate metering options
        /// </summary>
        /// <param name="options">The Geneva metering exporter options</param>
        /// <param name="configuration">The configuration</param>
        /// <returns>Validation result</returns>
        public static ValidationResult SafeUpdateAndValidateOptions(this GenevaMeteringExporterOptions options, IConfiguration configuration)
        {
            // Set default export interval
            options.MetricExportIntervalMilliseconds = 1000;

            var updateResult = options.SafeUpdateOption(configuration);
            if (!updateResult.IsSuccess)
            {
                return updateResult;
            }

            var errors = new List<string>();

            // Validate MonitoringAccount
            if (string.IsNullOrEmpty(options.MonitoringAccount))
            {
                errors.Add("MonitoringAccount is required for Geneva Metrics. Please provide a monitoring account in configuration.");
            }

            // Validate MonitoringNamespace
            if (string.IsNullOrEmpty(options.MonitoringNamespace))
            {
                errors.Add("MonitoringNamespace is required for Geneva Metrics. Please provide a monitoring namespace in configuration.");
            }

            if (errors.Count > 0)
            {
                // Log specific configuration issues
                if (string.IsNullOrEmpty(options.MonitoringAccount))
                {
                    SubstrateMeteringEventSource.Log.MonitoringAccountMissing("DisabledAccount");
                }
                if (string.IsNullOrEmpty(options.MonitoringNamespace))
                {
                    SubstrateMeteringEventSource.Log.MonitoringNamespaceMissing("DisabledNamespace");
                }

                // Apply defaults instead of failing
                ApplyDefaults(options);
                // Return success with applied defaults rather than failure
                return ValidationResult.Success();
            }

            return ValidationResult.Success();
        }

        /// <summary>
        /// Applies default values for Geneva metering options when configuration is missing
        /// </summary>
        /// <param name="options">The Geneva metering exporter options</param>
        public static void ApplyDefaults(this GenevaMeteringExporterOptions options)
        {
            if (string.IsNullOrEmpty(options.MonitoringAccount))
            {
                options.MonitoringAccount = "DisabledAccount";
            }

            if (string.IsNullOrEmpty(options.MonitoringNamespace))
            {
                options.MonitoringNamespace = "DisabledNamespace";
            }
        }
    }
}
