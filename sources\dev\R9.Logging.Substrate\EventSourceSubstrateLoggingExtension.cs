// <copyright file="EventSourceSubstrateLoggingExtension.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Internal;
using Microsoft.R9.Extensions.Enrichment;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate
{
    /// <summary>
    /// Extension methods for Substrate logging with comprehensive EventSource diagnostics
    /// These methods never throw exceptions and provide detailed telemetry for troubleshooting
    /// </summary>
    public static class EventSourceSubstrateLoggingExtension
    {
        /// <summary>
        /// Add substrate logging with comprehensive EventSource diagnostics
        /// </summary>
        /// <param name="serviceCollection">The service collection</param>
        /// <param name="serviceConfiguration">The service configuration</param>
        /// <param name="configure">Optional configuration action</param>
        /// <returns>The service collection</returns>
        public static IServiceCollection AddEventSourceSubstrateLogging(
            this IServiceCollection serviceCollection,
            IConfiguration serviceConfiguration,
            Action<ILoggingBuilder>? configure = null)
        {
            var version = GetSubstrateVersion();
            SubstrateLoggingEventSource.Log.SubstrateInitializationStarted(version);

            var issueCount = 0;
            var status = "Success";

            try
            {
                serviceCollection.AddLogging(loggingBuilder =>
                {
                    configure?.Invoke(loggingBuilder);

                    var result = loggingBuilder.ConfigureEventSourceSubstrateLogging(serviceConfiguration);
                    if (!result.IsSuccess)
                    {
                        issueCount = result.Errors.Count;
                        status = "CompletedWithIssues";
                        SubstrateLoggingEventSource.Log.LogValidationResult("SubstrateLoggingConfiguration", result);
                    }
                });
            }
            catch (Exception ex)
            {
                issueCount = 1;
                status = "Failed";
                SubstrateLoggingEventSource.Log.LogException("AddEventSourceSubstrateLogging", ex);
            }
            finally
            {
                SubstrateLoggingEventSource.Log.SubstrateInitializationCompleted(issueCount, status);
            }

            return serviceCollection;
        }

        /// <summary>
        /// Configure substrate logging with EventSource diagnostics
        /// </summary>
        /// <param name="loggingBuilder">The logging builder</param>
        /// <param name="serviceConfiguration">The service configuration</param>
        /// <returns>Validation result indicating success or failure</returns>
        public static ValidationResult ConfigureEventSourceSubstrateLogging(
            this ILoggingBuilder loggingBuilder,
            IConfiguration serviceConfiguration)
        {
            var allResults = new List<ValidationResult>();

            // Configure logging options with EventSource diagnostics
            var loggingOptionsResult = ConfigureLoggingOptionsWithEventSource(loggingBuilder, serviceConfiguration);
            allResults.Add(loggingOptionsResult);

            // Configure exporter with EventSource diagnostics
            var exporterResult = ConfigureExporterWithEventSource(loggingBuilder, serviceConfiguration);
            allResults.Add(exporterResult);

            // Configure enrichers with EventSource diagnostics
            var enricherResult = ConfigureEnrichersWithEventSource(loggingBuilder);
            allResults.Add(enricherResult);

            return ValidationResult.Combine(allResults.ToArray());
        }

        /// <summary>
        /// Configure logging options with EventSource diagnostics
        /// </summary>
        private static ValidationResult ConfigureLoggingOptionsWithEventSource(ILoggingBuilder loggingBuilder, IConfiguration serviceConfiguration)
        {
            try
            {
                loggingBuilder.AddOpenTelemetryLogging(options =>
                {
                    var result = options.SafeUpdateAndValidateOptions(serviceConfiguration);
                    if (!result.IsSuccess)
                    {
                        SubstrateLoggingEventSource.Log.LogValidationResult("LoggingOptions", result);
                    }
                });
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                var error = $"Failed to configure OpenTelemetry logging: {ex.Message}";
                SubstrateLoggingEventSource.Log.LogException("ConfigureLoggingOptions", ex);
                return ValidationResult.Failure(error);
            }
        }

        /// <summary>
        /// Configure exporter with EventSource diagnostics
        /// </summary>
        private static ValidationResult ConfigureExporterWithEventSource(ILoggingBuilder loggingBuilder, IConfiguration serviceConfiguration)
        {
            try
            {
                var useCompositeExporter = serviceConfiguration.GetValue("SubstrateLogging:UseCompositeExporter", false);
                
                if (useCompositeExporter)
                {
                    return ConfigureCompositeExporterWithEventSource(loggingBuilder, serviceConfiguration);
                }
                else
                {
                    return ConfigureGenevaExporterWithEventSource(loggingBuilder, serviceConfiguration);
                }
            }
            catch (Exception ex)
            {
                SubstrateLoggingEventSource.Log.ExporterConfigurationFailed("Unknown", ex.Message, "Console fallback");
                
                // Try console fallback
                try
                {
                    loggingBuilder.AddConsole();
                    return ValidationResult.Success();
                }
                catch (Exception consoleEx)
                {
                    SubstrateLoggingEventSource.Log.LogException("ConsoleFallback", consoleEx);
                    return ValidationResult.Failure($"Exporter configuration failed: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Configure composite exporter with EventSource diagnostics
        /// </summary>
        private static ValidationResult ConfigureCompositeExporterWithEventSource(ILoggingBuilder loggingBuilder, IConfiguration serviceConfiguration)
        {
            try
            {
                var compositeSection = serviceConfiguration.GetSection("SubstrateLogging:CompositeExporter");
                if (!compositeSection.Exists())
                {
                    SubstrateLoggingEventSource.Log.CompositeExporterConfigurationNotFound("Geneva");
                    return ConfigureGenevaExporterWithEventSource(loggingBuilder, serviceConfiguration);
                }

                // Safe ODL TCP configuration
                try
                {
                    serviceConfiguration.GetSection("SubstrateLogging:CompositeExporter:OdlTcp").ForceOdlTcpExportFormat();
                }
                catch (Exception ex)
                {
                    SubstrateLoggingEventSource.Log.OdlTcpConfigurationWarning(ex.Message);
                }

                loggingBuilder.ConfigureCompositeExporter(compositeSection);
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                SubstrateLoggingEventSource.Log.ExporterConfigurationFailed("Composite", ex.Message, "Geneva fallback");
                return ConfigureGenevaExporterWithEventSource(loggingBuilder, serviceConfiguration);
            }
        }

        /// <summary>
        /// Configure Geneva exporter with EventSource diagnostics
        /// </summary>
        private static ValidationResult ConfigureGenevaExporterWithEventSource(ILoggingBuilder loggingBuilder, IConfiguration serviceConfiguration)
        {
            try
            {
                loggingBuilder.AddGenevaExporter(options =>
                {
                    var result = options.SafeUpdateAndValidateOptions(serviceConfiguration);
                    if (!result.IsSuccess)
                    {
                        SubstrateLoggingEventSource.Log.LogValidationResult("GenevaExporter", result);
                        options.ApplyDefaults();
                    }
                });
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                SubstrateLoggingEventSource.Log.ExporterConfigurationFailed("Geneva", ex.Message, "None");
                return ValidationResult.Failure($"Geneva exporter configuration failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Configure enrichers with EventSource diagnostics
        /// </summary>
        private static ValidationResult ConfigureEnrichersWithEventSource(ILoggingBuilder loggingBuilder)
        {
            try
            {
                loggingBuilder.Services.AddLogEnricher<B2PassiveLogEnricher>();
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                SubstrateLoggingEventSource.Log.EnricherRegistrationFailed(nameof(B2PassiveLogEnricher), ex.Message);
                // Don't fail the entire configuration for enricher issues
                return ValidationResult.Success();
            }
        }

        /// <summary>
        /// Get the substrate version for diagnostics
        /// </summary>
        private static string GetSubstrateVersion()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return version?.ToString() ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }
    }

    /// <summary>
    /// Extension methods for EventSource-based configuration monitoring
    /// </summary>
    public static class EventSourceConfigurationExtensions
    {
        /// <summary>
        /// Monitor configuration changes and log them via EventSource
        /// </summary>
        /// <param name="configuration">The configuration to monitor</param>
        /// <param name="sectionPath">The section path to monitor</param>
        public static void MonitorConfigurationChanges(this IConfiguration configuration, string sectionPath)
        {
            try
            {
                configuration.GetReloadToken().RegisterChangeCallback(state =>
                {
                    try
                    {
                        var section = configuration.GetSection(sectionPath);
                        if (section.Exists())
                        {
                            // Log configuration reload
                            SubstrateLoggingEventSource.Log.UnexpectedError("ConfigurationReload", $"Configuration section '{sectionPath}' reloaded");
                        }
                    }
                    catch (Exception ex)
                    {
                        SubstrateLoggingEventSource.Log.LogException("ConfigurationMonitoring", ex);
                    }
                }, null);
            }
            catch (Exception ex)
            {
                SubstrateLoggingEventSource.Log.LogException("SetupConfigurationMonitoring", ex);
            }
        }
    }
}
