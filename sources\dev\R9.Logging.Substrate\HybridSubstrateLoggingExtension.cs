// <copyright file="HybridSubstrateLoggingExtension.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Internal;
using Microsoft.R9.Extensions.Enrichment;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate
{
    /// <summary>
    /// Hybrid extension methods that combine IValidateOptions pattern with safe fallbacks
    /// </summary>
    public static class HybridSubstrateLoggingExtension
    {
        /// <summary>
        /// Add substrate logging with hybrid validation approach
        /// Uses IValidateOptions for standard validation but provides safe fallbacks
        /// </summary>
        /// <param name="serviceCollection">The service collection</param>
        /// <param name="serviceConfiguration">The service configuration</param>
        /// <param name="configure">Optional configuration action</param>
        /// <param name="onValidationIssues">Optional callback for validation issues (non-fatal)</param>
        /// <returns>The service collection</returns>
        public static IServiceCollection AddHybridSubstrateLogging(
            this IServiceCollection serviceCollection,
            IConfiguration serviceConfiguration,
            Action<ILoggingBuilder>? configure = null,
            Action<string[]>? onValidationIssues = null)
        {
            // Configure options with validation
            serviceCollection.Configure<LoggingOptions>(
                serviceConfiguration.GetSection("SubstrateLogging:R9Logging"));
            
            serviceCollection.Configure<GenevaLogExporterOptions>(
                serviceConfiguration.GetSection("SubstrateLogging:GenevaExporter"));

            // Register safe validators that apply defaults instead of throwing
            serviceCollection.AddSingleton<IValidateOptions<LoggingOptions>, SafeLoggingOptionsValidator>();
            serviceCollection.AddSingleton<IValidateOptions<GenevaLogExporterOptions>, SafeGenevaLogExporterOptionsValidator>();

            // Register safe options wrappers with fallbacks
            serviceCollection.AddSafeOptionsValidation(new LoggingOptions());
            serviceCollection.AddSafeOptionsValidation(CreateDefaultGenevaOptions());

            // Configure logging
            serviceCollection.AddLogging(loggingBuilder =>
            {
                configure?.Invoke(loggingBuilder);
                
                ConfigureHybridLogging(loggingBuilder, serviceConfiguration, onValidationIssues);
            });

            return serviceCollection;
        }

        /// <summary>
        /// Configure hybrid logging that uses both validation approaches
        /// </summary>
        private static void ConfigureHybridLogging(
            ILoggingBuilder loggingBuilder, 
            IConfiguration serviceConfiguration,
            Action<string[]>? onValidationIssues)
        {
            // Configure OpenTelemetry logging with safe options access
            loggingBuilder.AddOpenTelemetryLogging((options, serviceProvider) =>
            {
                var safeOptions = serviceProvider.GetService<SafeOptions<LoggingOptions>>();
                if (safeOptions != null)
                {
                    var result = safeOptions.TryGetValue();
                    if (!result.IsSuccess)
                    {
                        onValidationIssues?.Invoke(result.Errors.ToArray());
                    }
                    
                    // Copy validated/defaulted values to the options
                    var validatedOptions = safeOptions.Value;
                    CopyLoggingOptions(validatedOptions, options);
                }
            });

            // Configure exporter safely
            ConfigureExporterHybrid(loggingBuilder, serviceConfiguration, onValidationIssues);

            // Add enrichers safely
            try
            {
                loggingBuilder.Services.AddLogEnricher<B2PassiveLogEnricher>();
            }
            catch (Exception ex)
            {
                onValidationIssues?.Invoke(new[] { $"Failed to add enricher: {ex.Message}" });
            }
        }

        /// <summary>
        /// Configure exporter using hybrid approach
        /// </summary>
        private static void ConfigureExporterHybrid(
            ILoggingBuilder loggingBuilder, 
            IConfiguration serviceConfiguration,
            Action<string[]>? onValidationIssues)
        {
            try
            {
                var useCompositeExporter = serviceConfiguration.GetValue("SubstrateLogging:UseCompositeExporter", false);
                
                if (useCompositeExporter)
                {
                    ConfigureCompositeExporterHybrid(loggingBuilder, serviceConfiguration, onValidationIssues);
                }
                else
                {
                    ConfigureGenevaExporterHybrid(loggingBuilder, onValidationIssues);
                }
            }
            catch (Exception ex)
            {
                onValidationIssues?.Invoke(new[] { $"Exporter configuration failed: {ex.Message}" });
                
                // Fallback to console logging
                try
                {
                    loggingBuilder.AddConsole();
                }
                catch
                {
                    // If even console fails, we'll continue without additional exporters
                }
            }
        }

        /// <summary>
        /// Configure Geneva exporter using hybrid validation
        /// </summary>
        private static void ConfigureGenevaExporterHybrid(
            ILoggingBuilder loggingBuilder,
            Action<string[]>? onValidationIssues)
        {
            loggingBuilder.AddGenevaExporter((options, serviceProvider) =>
            {
                var safeOptions = serviceProvider.GetService<SafeOptions<GenevaLogExporterOptions>>();
                if (safeOptions != null)
                {
                    var result = safeOptions.TryGetValue();
                    if (!result.IsSuccess)
                    {
                        onValidationIssues?.Invoke(result.Errors.ToArray());
                    }
                    
                    // Copy validated/defaulted values to the options
                    var validatedOptions = safeOptions.Value;
                    CopyGenevaOptions(validatedOptions, options);
                }
            });
        }

        /// <summary>
        /// Configure composite exporter with hybrid validation
        /// </summary>
        private static void ConfigureCompositeExporterHybrid(
            ILoggingBuilder loggingBuilder,
            IConfiguration serviceConfiguration,
            Action<string[]>? onValidationIssues)
        {
            try
            {
                var compositeSection = serviceConfiguration.GetSection("SubstrateLogging:CompositeExporter");
                if (!compositeSection.Exists())
                {
                    onValidationIssues?.Invoke(new[] { "Composite exporter configuration not found, falling back to Geneva exporter" });
                    ConfigureGenevaExporterHybrid(loggingBuilder, onValidationIssues);
                    return;
                }

                // Safe ODL TCP configuration
                try
                {
                    serviceConfiguration.GetSection("SubstrateLogging:CompositeExporter:OdlTcp").ForceOdlTcpExportFormat();
                }
                catch (Exception ex)
                {
                    onValidationIssues?.Invoke(new[] { $"ODL TCP configuration warning: {ex.Message}" });
                }

                loggingBuilder.ConfigureCompositeExporter(compositeSection);
            }
            catch (Exception ex)
            {
                onValidationIssues?.Invoke(new[] { $"Composite exporter failed: {ex.Message}, falling back to Geneva" });
                ConfigureGenevaExporterHybrid(loggingBuilder, onValidationIssues);
            }
        }

        /// <summary>
        /// Create default Geneva options for fallback
        /// </summary>
        private static GenevaLogExporterOptions CreateDefaultGenevaOptions()
        {
            var options = new GenevaLogExporterOptions();
            options.ApplyDefaults();
            return options;
        }

        /// <summary>
        /// Copy logging options properties safely
        /// </summary>
        private static void CopyLoggingOptions(LoggingOptions source, LoggingOptions target)
        {
            try
            {
                // Copy properties from validated source to target
                // Add specific property copying logic based on LoggingOptions structure
                target.MaxStackTraceLength = source.MaxStackTraceLength;
                // Add other properties as needed
            }
            catch
            {
                // If copying fails, target keeps its default values
            }
        }

        /// <summary>
        /// Copy Geneva options properties safely
        /// </summary>
        private static void CopyGenevaOptions(GenevaLogExporterOptions source, GenevaLogExporterOptions target)
        {
            try
            {
                target.ConnectionString = source.ConnectionString;
                target.TableNameMappings = source.TableNameMappings;
                // Add other properties as needed
            }
            catch
            {
                // If copying fails, target keeps its default values
            }
        }
    }
}
