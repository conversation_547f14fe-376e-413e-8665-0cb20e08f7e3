// <copyright file="SafeSubstrateLoggingExtensionNoLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Internal;
using Microsoft.R9.Extensions.Enrichment;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate
{
    /// <summary>
    /// Safe extension methods for Substrate logging that don't throw exceptions or use ILogger. 
    /// These methods provide graceful fallbacks when configuration is invalid.
    /// </summary>
    public static class SafeSubstrateLoggingExtension
    {
        /// <summary>
        /// Safely add logging for Substrate owner with graceful error handling
        /// </summary>
        /// <param name="serviceCollection">The service collection</param>
        /// <param name="serviceConfiguration">The service configuration</param>
        /// <param name="configure">Optional configuration action</param>
        /// <param name="onValidationFailure">Optional callback for validation failures</param>
        /// <returns>The service collection</returns>
        public static IServiceCollection AddSafeSubstrateLogging(
            this IServiceCollection serviceCollection,
            IConfiguration serviceConfiguration,
            Action<ILoggingBuilder>? configure = null,
            Action<ValidationResult>? onValidationFailure = null)
        {
            serviceCollection.AddLogging(loggingBuilder =>
            {
                // Load user customized actions for logging builder if there is
                configure?.Invoke(loggingBuilder);

                var result = loggingBuilder.ConfigureSafeSubstrateLogging(serviceConfiguration);
                if (!result.IsSuccess)
                {
                    onValidationFailure?.Invoke(result);
                }
            });

            return serviceCollection;
        }

        /// <summary>
        /// Safely configure substrate logging with graceful error handling
        /// </summary>
        /// <param name="loggingBuilder">The logging builder</param>
        /// <param name="serviceConfiguration">The service configuration</param>
        /// <param name="onValidationFailure">Optional callback for validation failures</param>
        /// <returns>Validation result indicating success or failure</returns>
        public static ValidationResult ConfigureSafeSubstrateLogging(
            this ILoggingBuilder loggingBuilder,
            IConfiguration serviceConfiguration,
            Action<ValidationResult>? onValidationFailure = null)
        {
            try
            {
                var allResults = new List<ValidationResult>();

                // Handle configurations and setup with safe validation
                var loggingOptionsResult = ConfigureLoggingOptions(loggingBuilder, serviceConfiguration);
                allResults.Add(loggingOptionsResult);

                // Choose exporter with safe configuration
                var exporterResult = ConfigureExporter(loggingBuilder, serviceConfiguration);
                allResults.Add(exporterResult);

                // Add enrichers (this is safe and shouldn't fail)
                var enricherResult = ConfigureEnrichers(loggingBuilder);
                allResults.Add(enricherResult);

                var combinedResult = ValidationResult.Combine(allResults.ToArray());
                if (!combinedResult.IsSuccess)
                {
                    onValidationFailure?.Invoke(combinedResult);
                }

                return combinedResult;
            }
            catch (Exception ex)
            {
                var error = $"Unexpected error during safe substrate logging configuration: {ex.Message}";
                var result = ValidationResult.Failure(error);
                onValidationFailure?.Invoke(result);
                return result;
            }
        }

        /// <summary>
        /// Safely configure logging options
        /// </summary>
        private static ValidationResult ConfigureLoggingOptions(ILoggingBuilder loggingBuilder, IConfiguration serviceConfiguration)
        {
            try
            {
                loggingBuilder.AddOpenTelemetryLogging(options =>
                {
                    var result = options.SafeUpdateAndValidateOptions(serviceConfiguration);
                    // Note: We don't fail here even if validation fails, as defaults are applied
                });
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                var error = $"Failed to configure OpenTelemetry logging: {ex.Message}";
                return ValidationResult.Failure(error);
            }
        }

        /// <summary>
        /// Safely configure exporter
        /// </summary>
        private static ValidationResult ConfigureExporter(ILoggingBuilder loggingBuilder, IConfiguration serviceConfiguration)
        {
            try
            {
                var useCompositeExporter = serviceConfiguration.GetValue("SubstrateLogging:UseCompositeExporter", false);
                
                if (useCompositeExporter)
                {
                    return ConfigureCompositeExporter(loggingBuilder, serviceConfiguration);
                }
                else
                {
                    return ConfigureGenevaExporter(loggingBuilder, serviceConfiguration);
                }
            }
            catch (Exception ex)
            {
                var error = $"Failed to configure exporter: {ex.Message}";
                
                // Try to configure a basic console exporter as fallback
                try
                {
                    loggingBuilder.AddConsole();
                    return ValidationResult.Success();
                }
                catch
                {
                    return ValidationResult.Failure(error);
                }
            }
        }

        /// <summary>
        /// Safely configure composite exporter
        /// </summary>
        private static ValidationResult ConfigureCompositeExporter(ILoggingBuilder loggingBuilder, IConfiguration serviceConfiguration)
        {
            try
            {
                var compositeSection = serviceConfiguration.GetSection("SubstrateLogging:CompositeExporter");
                if (!compositeSection.Exists())
                {
                    // Composite exporter configuration not found, fall back to Geneva exporter
                    return ConfigureGenevaExporter(loggingBuilder, serviceConfiguration);
                }

                // Safe ODL TCP configuration
                try
                {
                    serviceConfiguration.GetSection("SubstrateLogging:CompositeExporter:OdlTcp").ForceOdlTcpExportFormat();
                }
                catch
                {
                    // Failed to configure ODL TCP export format, continue anyway
                }

                loggingBuilder.ConfigureCompositeExporter(compositeSection);
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                var error = $"Failed to configure composite exporter: {ex.Message}";
                
                // Fallback to Geneva exporter
                return ConfigureGenevaExporter(loggingBuilder, serviceConfiguration);
            }
        }

        /// <summary>
        /// Safely configure Geneva exporter
        /// </summary>
        private static ValidationResult ConfigureGenevaExporter(ILoggingBuilder loggingBuilder, IConfiguration serviceConfiguration)
        {
            try
            {
                loggingBuilder.AddGenevaExporter(options =>
                {
                    var result = options.SafeUpdateAndValidateOptions(serviceConfiguration);
                    if (!result.IsSuccess)
                    {
                        // Apply defaults when validation fails
                        options.ApplyDefaults();
                    }
                });
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                var error = $"Failed to configure Geneva exporter: {ex.Message}";
                return ValidationResult.Failure(error);
            }
        }

        /// <summary>
        /// Safely configure enrichers
        /// </summary>
        private static ValidationResult ConfigureEnrichers(ILoggingBuilder loggingBuilder)
        {
            try
            {
                loggingBuilder.Services.AddLogEnricher<B2PassiveLogEnricher>();
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                // Don't fail the entire configuration if enricher fails
                return ValidationResult.Success();
            }
        }
    }
}
