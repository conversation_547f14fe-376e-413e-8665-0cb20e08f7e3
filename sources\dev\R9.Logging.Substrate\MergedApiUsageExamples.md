# Merged API Usage Examples

## Overview

The R9 Substrate extensions now provide a unified API that:
- **Never throws exceptions** - Safe for customer services
- **Uses EventSource internally** - Controlled by ECS configuration
- **Maintains backward compatibility** - Existing code works unchanged
- **Provides optional diagnostics** - For advanced scenarios

## Basic Usage (Recommended for Most Customers)

### Logging
```csharp
// Simple usage - same as before, but now safe
services.AddSubstrateLogging(configuration);

// With custom logging configuration
services.AddSubstrateLogging(configuration, loggingBuilder =>
{
    loggingBuilder.SetMinimumLevel(LogLevel.Information);
    loggingBuilder.AddConsole();
});
```

### Metering
```csharp
// Simple usage - same as before, but now safe
services.AddSubstrateMetering(configuration);

// With custom metering configuration
services.AddSubstrateMetering(configuration, meterProviderBuilder =>
{
    meterProviderBuilder.AddMeter("MyCustomMeter");
});
```

## Advanced Usage (For Microsoft Internal Services)

### With Validation Callbacks
```csharp
// Get notified of configuration issues for diagnostics
services.AddSubstrateLogging(configuration, 
    configure: loggingBuilder => 
    {
        loggingBuilder.AddConsole();
    },
    onValidationFailure: result =>
    {
        // Handle configuration issues gracefully
        Console.WriteLine($"Substrate logging issues: {result.Errors.Count}");
        foreach (var error in result.Errors)
        {
            Console.WriteLine($"  - {error}");
        }
        
        // Could send to your monitoring system
        // MyMonitoring.TrackSubstrateIssues(result.Errors);
    });

// Same for metering
services.AddSubstrateMetering(configuration,
    configure: meterProviderBuilder =>
    {
        meterProviderBuilder.AddMeter("MyService");
    },
    onValidationFailure: result =>
    {
        // Handle metering configuration issues
        MyTelemetry.TrackMeteringIssues(result.Errors);
    });
```

### Direct Configuration
```csharp
// Configure logging builder directly
loggingBuilder.ConfigureSubstrateLogging(configuration, result =>
{
    if (!result.IsSuccess)
    {
        // Handle configuration issues
        MyDiagnostics.LogSubstrateIssues(result.Errors);
    }
});

// Configure meter provider builder directly
meterProviderBuilder.ConfigureSubstrateMetering(configuration, result =>
{
    if (!result.IsSuccess)
    {
        // Handle configuration issues
        MyDiagnostics.LogMeteringIssues(result.Errors);
    }
});
```

## EventSource Control

### Environment Variable (Testing/Development)
```bash
# Enable EventSource diagnostics for testing
set R9_SUBSTRATE_EVENTSOURCE_ENABLED=true

# Run your application - EventSource events will be emitted
dotnet run
```

### ECS Configuration (Production)
```json
{
  "ECSParameters": {
    "DiagnosticsSettings": {
      "SubstrateEventSourceEnabled": true
    }
  }
}
```

## Migration Guide

### Existing Code (No Changes Required)
```csharp
// This code continues to work exactly as before
services.AddSubstrateLogging(configuration);
services.AddSubstrateMetering(configuration);

// But now it's safe and won't crash your service
```

### Enhanced Code (Optional)
```csharp
// Add validation callbacks for better diagnostics
services.AddSubstrateLogging(configuration, onValidationFailure: result =>
{
    // Log configuration issues to your monitoring system
    MyMonitoring.TrackConfigurationIssues("SubstrateLogging", result.Errors);
});

services.AddSubstrateMetering(configuration, onValidationFailure: result =>
{
    // Log configuration issues to your monitoring system  
    MyMonitoring.TrackConfigurationIssues("SubstrateMetering", result.Errors);
});
```

## Error Handling Examples

### Configuration Missing
```csharp
// If configuration is missing, substrate applies safe defaults
// and continues working (no exceptions thrown)

services.AddSubstrateLogging(configuration, onValidationFailure: result =>
{
    // You'll get notified about missing configuration
    // Example errors:
    // - "Configuration section 'SubstrateLogging:GenevaExporter' not found"
    // - "Required configuration 'ConnectionString' missing, applied default"
});
```

### Invalid Configuration
```csharp
// If configuration is invalid, substrate applies safe defaults
// and continues working (no exceptions thrown)

services.AddSubstrateMetering(configuration, onValidationFailure: result =>
{
    // You'll get notified about invalid configuration
    // Example errors:
    // - "MonitoringAccount is required, applied default 'DisabledAccount'"
    // - "Invalid environment type 'InvalidEnv', using 'Production'"
});
```

### Network/Infrastructure Issues
```csharp
// If Geneva/ETW is unavailable, substrate falls back gracefully
// and continues working (no exceptions thrown)

services.AddSubstrateLogging(configuration, onValidationFailure: result =>
{
    // You'll get notified about infrastructure issues
    // Example errors:
    // - "Geneva exporter configuration failed, using console fallback"
    // - "ETW session unavailable, using disabled session"
});
```

## Benefits Summary

### For All Customers
- **Zero crashes** - Configuration errors never crash services
- **Same API** - No code changes required
- **Better reliability** - Graceful fallbacks for all error scenarios
- **Privacy protected** - EventSource disabled by default

### For Microsoft Internal Services
- **Rich diagnostics** - Optional validation callbacks
- **EventSource telemetry** - When enabled via ECS
- **Distributed monitoring** - Collect issues across services
- **Proactive troubleshooting** - Identify configuration drift

### For Operations Teams
- **Centralized control** - Enable/disable diagnostics via ECS
- **Structured events** - ETW integration for monitoring
- **No customer impact** - Diagnostic failures never affect services
- **Audit trail** - ECS configuration changes are tracked

This merged approach provides the best of both worlds: bulletproof reliability for customers with comprehensive diagnostics for Microsoft internal operations.
