// <copyright file="SafeEcsParameterValidatorNoLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Linq;
using Microsoft.Extensions.Configuration;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Internal;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Configuration
{
    /// <summary>
    /// Provides safe extension methods for validating ECS parameter options without throwing exceptions or using ILogger.
    /// </summary>
    internal static class SafeEcsParameterValidator
    {
        private const string EcsParametersSectionKey = "ECSParameters";
        private const string ServiceIdentifierKey = $"{EcsParametersSectionKey}:ECSIdentifiers:ServiceName";
        private const string EnvironmentTypeKey = $"{EcsParametersSectionKey}:EnvironmentType";
        private const string AuthOptionKey = $"{EcsParametersSectionKey}:AuthenticationOption";

        /// <summary>
        /// Safely binds ECS parameters from configuration and validates them without throwing exceptions.
        /// </summary>
        /// <param name="configuration">The configuration source.</param>
        /// <returns>Validation result with ECS type if successful</returns>
        internal static ValidationResult<ECSType> SafeValidateAndDetermineECSType(this IConfiguration configuration)
        {
            if (configuration == null)
            {
                var error = "Configuration cannot be null";
                return ValidationResult<ECSType>.Failure(error);
            }

            try
            {
                var ecsSection = configuration.GetSection(EcsParametersSectionKey);
                if (!ecsSection.Exists())
                {
                    // ECS configuration section not found, use default centralized production ECS
                    return ValidationResult<ECSType>.Success(ECSType.CentralizedProd);
                }

                var options = ecsSection.Get<EcsParametersOptions>();
                if (options == null)
                {
                    // Could not parse ECS configuration section, use default centralized production ECS
                    return ValidationResult<ECSType>.Success(ECSType.CentralizedProd);
                }

                var validationResult = ValidateRequiredFields(options);
                if (!validationResult.IsSuccess)
                {
                    // If required fields are missing, apply defaults and continue
                    ApplyDefaultEcsConfiguration(options);
                }

                return DetermineECSTypeSafely(options, configuration);
            }
            catch (Exception ex)
            {
                var error = $"Unexpected error during ECS configuration validation: {ex.Message}";
                // Return safe fallback instead of failing
                return ValidationResult<ECSType>.Success(ECSType.CentralizedProd);
            }
        }

        /// <summary>
        /// Validates that required fields are present in the configuration.
        /// </summary>
        private static ValidationResult ValidateRequiredFields(EcsParametersOptions options)
        {
            if (options.ECSIdentifiers == null || string.IsNullOrEmpty(options.ECSIdentifiers.ServiceName))
            {
                var error = $"Required ECS service name is missing from '{ServiceIdentifierKey}'";
                return ValidationResult.Failure(error);
            }

            return ValidationResult.Success();
        }

        /// <summary>
        /// Safely determines the ECS type without throwing exceptions
        /// </summary>
        private static ValidationResult<ECSType> DetermineECSTypeSafely(EcsParametersOptions options, IConfiguration configuration)
        {
            try
            {
                bool hasAuthenticationSettings = configuration.GetSection(AuthOptionKey).Exists() &&
                                                 configuration.GetSection(AuthOptionKey).GetChildren().Any();

                bool hasClientValue = !string.IsNullOrEmpty(options.Client);
                bool hasAgentsValue = options.Agents != null && options.Agents.Any();
                bool hasEnvironmentType = !string.IsNullOrEmpty(options.EnvironmentType);

                var customizationValidation = ValidateCustomizationCompleteness(hasClientValue, hasAgentsValue, hasEnvironmentType);
                if (!customizationValidation.IsSuccess)
                {
                    // Customization incomplete, use default configuration
                    return ValidationResult<ECSType>.Success(ECSType.CentralizedProd);
                }

                if (hasEnvironmentType)
                {
                    var envValidation = ValidateEnvironmentType(options.EnvironmentType);
                    if (!envValidation.IsSuccess)
                    {
                        // Invalid environment type, use default configuration
                        return ValidationResult<ECSType>.Success(ECSType.CentralizedProd);
                    }
                }

                if (UseCentralizedIntECSEndpoint(options))
                {
                    return ValidationResult<ECSType>.Success(ECSType.CentralizedInt);
                }

                bool hasCustomClient = hasClientValue && !string.Equals(options.Client, Constants.ECSClientName, StringComparison.OrdinalIgnoreCase);
                bool hasCustomAgents = hasAgentsValue && !options.Agents!.SetEquals(Constants.ECSTeamNameProd);
                bool useCustomEcs = hasCustomClient || hasCustomAgents;

                if (useCustomEcs && !hasAuthenticationSettings)
                {
                    // Custom ECS configuration detected but authentication settings are missing
                    // Fall back to centralized production ECS instead of failing
                    return ValidationResult<ECSType>.Success(ECSType.CentralizedProd);
                }

                return ValidationResult<ECSType>.Success(useCustomEcs ? ECSType.Custom : ECSType.CentralizedProd);
            }
            catch (Exception ex)
            {
                var error = $"Error determining ECS type: {ex.Message}";
                // Return safe fallback instead of failing
                return ValidationResult<ECSType>.Success(ECSType.CentralizedProd);
            }
        }

        /// <summary>
        /// Validates customization completeness without throwing exceptions
        /// </summary>
        private static ValidationResult ValidateCustomizationCompleteness(bool hasClientValue, bool hasAgentsValue, bool hasEnvironmentType)
        {
            bool hasAnyCustomization = hasClientValue || hasAgentsValue || hasEnvironmentType;
            bool allCustomizationsProvided = hasClientValue && hasAgentsValue && hasEnvironmentType;

            if (hasAnyCustomization && !allCustomizationsProvided)
            {
                var error = "Incomplete ECS configuration detected. When customizing any ECS setting, " +
                           "you must provide all three: Client, Agents, and EnvironmentType in the ECSParameters section.";
                return ValidationResult.Failure(error);
            }

            return ValidationResult.Success();
        }

        /// <summary>
        /// Validates environment type without throwing exceptions
        /// </summary>
        private static ValidationResult ValidateEnvironmentType(string environmentType)
        {
            bool isValidEnvironment = string.Equals(environmentType, "Integration", StringComparison.OrdinalIgnoreCase) ||
                                      string.Equals(environmentType, "Production", StringComparison.OrdinalIgnoreCase);

            if (!isValidEnvironment)
            {
                var error = $"Invalid value '{environmentType}' for environment type. " +
                           "Supported values are 'Production' or 'Integration'.";
                return ValidationResult.Failure(error);
            }

            return ValidationResult.Success();
        }

        /// <summary>
        /// Determines if the configuration specifies the centralized integration ECS endpoint.
        /// </summary>
        private static bool UseCentralizedIntECSEndpoint(EcsParametersOptions options)
        {
            try
            {
                return options.Client?.Equals(Constants.ECSClientName, StringComparison.OrdinalIgnoreCase) == true &&
                       options.Agents?.SetEquals(Constants.ECSTeamNameInt) == true &&
                       options.EnvironmentType?.Equals("Integration", StringComparison.OrdinalIgnoreCase) == true;
            }
            catch
            {
                return false; // Safe fallback
            }
        }

        /// <summary>
        /// Applies default ECS configuration when validation fails
        /// </summary>
        private static void ApplyDefaultEcsConfiguration(EcsParametersOptions options)
        {
            if (options.ECSIdentifiers == null)
            {
                options.ECSIdentifiers = new EcsIdentifiersOptions();
            }

            if (string.IsNullOrEmpty(options.ECSIdentifiers.ServiceName))
            {
                options.ECSIdentifiers.ServiceName = "DefaultService";
            }

            if (string.IsNullOrEmpty(options.Client))
            {
                options.Client = Constants.ECSClientName;
            }

            if (options.Agents == null || !options.Agents.Any())
            {
                options.Agents = Constants.ECSTeamNameProd.ToHashSet();
            }

            if (string.IsNullOrEmpty(options.EnvironmentType))
            {
                options.EnvironmentType = "Production";
            }
        }
    }
}
