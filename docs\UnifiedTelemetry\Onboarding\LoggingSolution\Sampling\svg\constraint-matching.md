```mermaid
%%{init: {'theme': 'forest', 'flowchart': {'curve': 'natural', 'nodeSpacing': 60, 'rankSpacing': 80, 'fontSize': 14}, 'themeVariables': {'fontFamily': 'Comic Sans MS, cursive', 'primaryTextColor': '#333', 'primaryColor': '#333', 'edgeLabelBackground': 'transparent', 'nodeBorder': '#333', 'textShadow': 'none'}}}%%
flowchart LR
    A[Check Constraint] --> B{Field Type}
    
    B -->|String| C1[String Operators]
    C1 --> |Equals/NotEquals/StartsWith/etc.| D
    
    B -->|Long| C2[Numeric Operators]
    C2 --> |==/>/</>=/<=/!=| D
    
    B -->|Enum| C3[Enum Operators]
    C3 --> |In/NotIn| D
    
    B -->|LogLevel| C4[LogLevel Operators]
    C4 --> |==/>/</etc.| D
    
    D{Apply Operator}
    D -->|Match| E[Constraint Passes]
    D -->|No Match| F[Constraint Fails]
    
    classDef default fill:#f9f9f9,stroke:#333,stroke-width:1px
```