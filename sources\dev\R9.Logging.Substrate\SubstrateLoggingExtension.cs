﻿// <copyright file="SubstrateLoggingExtension.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.R9.Extensions.Enrichment;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate
{
    /// <summary>
    /// Extension methods for Substrate logging. Refer ReadMe.md for more details.
    /// </summary>
    public static class SubstrateLoggingExtension
    {
        /// <summary>
        /// Add logging for Substrate owner
        /// </summary>
        /// <param name="serviceCollection"></param>
        /// <param name="serviceConfiguration"></param>
        /// <param name="configure"></param>
        /// <returns></returns>
        public static IServiceCollection AddSubstrateLogging(
            this IServiceCollection serviceCollection,
            IConfiguration serviceConfiguration,
            Action<ILoggingBuilder>? configure = null)
        {
            serviceCollection.AddLogging(loggingBuilder =>
            {
                // Load user customized actions for logging builder if there is
                configure?.Invoke(loggingBuilder);

                loggingBuilder.ConfigureSubstrateLogging(serviceConfiguration);
            });

            return serviceCollection;
        }

        /// <summary>
        /// Configure substrate to logging builder.
        /// </summary>
        /// <param name="loggingBuilder"></param>
        /// <param name="serviceConfiguration"></param>
        /// <returns></returns>
        public static ILoggingBuilder ConfigureSubstrateLogging(
            this ILoggingBuilder loggingBuilder,
            IConfiguration serviceConfiguration)
        {
            // Handle configurations and setup
            // Load from IConfiguration, Validate and Set defaut values
            loggingBuilder.AddOpenTelemetryLogging(options =>
            {
                options.UpdateAndValidateOptions(serviceConfiguration);
            });

            // Choose exporter, default to use single geneva exporter
            var useCompositeExporter = serviceConfiguration.GetValue("SubstrateLogging:UseCompositeExporter", false);
            if (useCompositeExporter)
            {
                serviceConfiguration.GetSection("SubstrateLogging:CompositeExporter:OdlTcp").ForceOdlTcpExportFormat();
                loggingBuilder.ConfigureCompositeExporter(serviceConfiguration.GetSection("SubstrateLogging:CompositeExporter"));
            }
            else
            {
                loggingBuilder.AddGenevaExporter(options =>
                {
                    options.UpdateAndValidateOptions(serviceConfiguration);
                });
            }

            // Enrichers
            loggingBuilder.Services.AddLogEnricher<B2PassiveLogEnricher>();
            return loggingBuilder;
        }
    }
}
