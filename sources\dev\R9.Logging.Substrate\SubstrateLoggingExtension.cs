// <copyright file="SubstrateLoggingExtension.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Reflection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Internal;
using Microsoft.R9.Extensions.Enrichment;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate
{
    /// <summary>
    /// Extension methods for Substrate logging. Refer ReadMe.md for more details.
    /// </summary>
    public static class SubstrateLoggingExtension
    {
        /// <summary>
        /// Add logging for Substrate owner
        /// </summary>
        /// <param name="serviceCollection"></param>
        /// <param name="serviceConfiguration"></param>
        /// <param name="configure"></param>
        /// <returns></returns>
        public static IServiceCollection AddSubstrateLogging(
            this IServiceCollection serviceCollection,
            IConfiguration serviceConfiguration,
            Action<ILoggingBuilder>? configure = null)
        {
            // Log initialization start with version info
            var version = GetSubstrateVersion();
            SubstrateLoggingEventSource.Log.SubstrateInitializationStarted(version);

            var issueCount = 0;
            var status = "Success";

            try
            {
                serviceCollection.AddLogging(loggingBuilder =>
                {
                    // Load user customized actions for logging builder if there is
                    configure?.Invoke(loggingBuilder);

                    var result = loggingBuilder.ConfigureSubstrateLoggingSafe(serviceConfiguration);
                    if (!result.IsSuccess)
                    {
                        issueCount = result.Errors.Count;
                        status = "CompletedWithIssues";
                        SubstrateLoggingEventSource.Log.LogValidationResult("SubstrateLoggingConfiguration", result);
                    }
                });
            }
            catch (Exception ex)
            {
                issueCount = 1;
                status = "Failed";
                SubstrateLoggingEventSource.Log.LogException("AddSubstrateLogging", ex);

                // Don't rethrow - apply safe fallback
                serviceCollection.AddLogging(loggingBuilder =>
                {
                    configure?.Invoke(loggingBuilder);
                    // Minimal safe configuration
                    try
                    {
                        loggingBuilder.AddConsole();
                    }
                    catch
                    {
                        // If even console fails, continue without additional logging
                    }
                });
            }
            finally
            {
                SubstrateLoggingEventSource.Log.SubstrateInitializationCompleted(issueCount, status);
            }

            return serviceCollection;
        }

        /// <summary>
        /// Configure substrate to logging builder.
        /// </summary>
        /// <param name="loggingBuilder"></param>
        /// <param name="serviceConfiguration"></param>
        /// <returns></returns>
        public static ILoggingBuilder ConfigureSubstrateLogging(
            this ILoggingBuilder loggingBuilder,
            IConfiguration serviceConfiguration)
        {
            // Use safe internal implementation
            var result = loggingBuilder.ConfigureSubstrateLoggingSafe(serviceConfiguration);

            // For backward compatibility, we don't expose the validation result
            // but we log any issues via EventSource for internal diagnostics
            if (!result.IsSuccess)
            {
                SubstrateLoggingEventSource.Log.LogValidationResult("ConfigureSubstrateLogging", result);
            }

            return loggingBuilder;
        }

        /// <summary>
        /// Internal safe configuration method that doesn't throw exceptions
        /// </summary>
        internal static ValidationResult ConfigureSubstrateLoggingSafe(
            this ILoggingBuilder loggingBuilder,
            IConfiguration serviceConfiguration)
        {
            var allResults = new List<ValidationResult>();

            // Configure logging options safely
            var loggingOptionsResult = ConfigureLoggingOptionsSafe(loggingBuilder, serviceConfiguration);
            allResults.Add(loggingOptionsResult);

            // Configure exporter safely
            var exporterResult = ConfigureExporterSafe(loggingBuilder, serviceConfiguration);
            allResults.Add(exporterResult);

            // Configure enrichers safely
            var enricherResult = ConfigureEnrichersSafe(loggingBuilder);
            allResults.Add(enricherResult);

            return ValidationResult.Combine(allResults.ToArray());
        }

        /// <summary>
        /// Configure logging options safely
        /// </summary>
        private static ValidationResult ConfigureLoggingOptionsSafe(ILoggingBuilder loggingBuilder, IConfiguration serviceConfiguration)
        {
            try
            {
                loggingBuilder.AddOpenTelemetryLogging(options =>
                {
                    var result = options.SafeUpdateAndValidateOptions(serviceConfiguration);
                    if (!result.IsSuccess)
                    {
                        SubstrateLoggingEventSource.Log.LogValidationResult("LoggingOptions", result);
                    }
                });
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                SubstrateLoggingEventSource.Log.LogException("ConfigureLoggingOptions", ex);
                return ValidationResult.Failure($"Failed to configure OpenTelemetry logging: {ex.Message}");
            }
        }

        /// <summary>
        /// Configure exporter safely
        /// </summary>
        private static ValidationResult ConfigureExporterSafe(ILoggingBuilder loggingBuilder, IConfiguration serviceConfiguration)
        {
            try
            {
                var useCompositeExporter = serviceConfiguration.GetValue("SubstrateLogging:UseCompositeExporter", false);

                if (useCompositeExporter)
                {
                    return ConfigureCompositeExporterSafe(loggingBuilder, serviceConfiguration);
                }
                else
                {
                    return ConfigureGenevaExporterSafe(loggingBuilder, serviceConfiguration);
                }
            }
            catch (Exception ex)
            {
                SubstrateLoggingEventSource.Log.ExporterConfigurationFailed("Unknown", ex.Message, "Console fallback");

                // Try console fallback
                try
                {
                    loggingBuilder.AddConsole();
                    return ValidationResult.Success();
                }
                catch (Exception consoleEx)
                {
                    SubstrateLoggingEventSource.Log.LogException("ConsoleFallback", consoleEx);
                    return ValidationResult.Failure($"Exporter configuration failed: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Configure composite exporter safely
        /// </summary>
        private static ValidationResult ConfigureCompositeExporterSafe(ILoggingBuilder loggingBuilder, IConfiguration serviceConfiguration)
        {
            try
            {
                var compositeSection = serviceConfiguration.GetSection("SubstrateLogging:CompositeExporter");
                if (!compositeSection.Exists())
                {
                    SubstrateLoggingEventSource.Log.CompositeExporterConfigurationNotFound("Geneva");
                    return ConfigureGenevaExporterSafe(loggingBuilder, serviceConfiguration);
                }

                // Safe ODL TCP configuration
                try
                {
                    serviceConfiguration.GetSection("SubstrateLogging:CompositeExporter:OdlTcp").ForceOdlTcpExportFormat();
                }
                catch (Exception ex)
                {
                    SubstrateLoggingEventSource.Log.OdlTcpConfigurationWarning(ex.Message);
                }

                loggingBuilder.ConfigureCompositeExporter(compositeSection);
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                SubstrateLoggingEventSource.Log.ExporterConfigurationFailed("Composite", ex.Message, "Geneva fallback");
                return ConfigureGenevaExporterSafe(loggingBuilder, serviceConfiguration);
            }
        }

        /// <summary>
        /// Configure Geneva exporter safely
        /// </summary>
        private static ValidationResult ConfigureGenevaExporterSafe(ILoggingBuilder loggingBuilder, IConfiguration serviceConfiguration)
        {
            try
            {
                loggingBuilder.AddGenevaExporter(options =>
                {
                    var result = options.SafeUpdateAndValidateOptions(serviceConfiguration);
                    if (!result.IsSuccess)
                    {
                        SubstrateLoggingEventSource.Log.LogValidationResult("GenevaExporter", result);
                        options.ApplyDefaults();
                    }
                });
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                SubstrateLoggingEventSource.Log.ExporterConfigurationFailed("Geneva", ex.Message, "None");
                return ValidationResult.Failure($"Geneva exporter configuration failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Configure enrichers safely
        /// </summary>
        private static ValidationResult ConfigureEnrichersSafe(ILoggingBuilder loggingBuilder)
        {
            try
            {
                loggingBuilder.Services.AddLogEnricher<B2PassiveLogEnricher>();
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                SubstrateLoggingEventSource.Log.EnricherRegistrationFailed(nameof(B2PassiveLogEnricher), ex.Message);
                // Don't fail the entire configuration for enricher issues
                return ValidationResult.Success();
            }
        }

        /// <summary>
        /// Get the substrate version for diagnostics
        /// </summary>
        private static string GetSubstrateVersion()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return version?.ToString() ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }
    }
}
