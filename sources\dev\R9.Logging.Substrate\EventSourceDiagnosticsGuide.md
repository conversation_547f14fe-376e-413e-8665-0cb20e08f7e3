# EventSource Diagnostics Guide for R9 Substrate Extensions

## Overview

The R9 Substrate extensions now use EventSource for comprehensive diagnostics without affecting customer service reliability. This approach provides:

- **Zero-crash guarantee** - Configuration errors never crash customer services
- **Rich diagnostics** - Detailed telemetry for troubleshooting distributed systems
- **High performance** - Minimal overhead when not being monitored
- **ETW integration** - Works with Windows Event Tracing and monitoring tools

## EventSource Names

- **Logging Substrate**: `Microsoft-M365-Core-Telemetry-R9-Logging-Substrate`
- **Metering Substrate**: `Microsoft-M365-Core-Telemetry-R9-Metering-Substrate`

## Usage Examples

### Basic Usage (Recommended for Customers)

```csharp
// Safe logging configuration - never crashes
services.AddEventSourceSubstrateLogging(configuration);

// Safe metering configuration - never crashes  
services.AddSafeSubstrateMetering(configuration);
```

### Advanced Usage with Error Monitoring

```csharp
// Monitor configuration issues
services.AddSafeSubstrateLogging(configuration, onValidationFailure: result =>
{
    // Handle configuration issues gracefully
    foreach (var error in result.Errors)
    {
        // Log to your application's logging system
        Console.WriteLine($"Substrate config issue: {error}");
    }
});
```

## Monitoring EventSource Events

### Using ETW (Windows Event Tracing)

```powershell
# Start ETW session
logman create trace SubstrateTrace -p "Microsoft-M365-Core-Telemetry-R9-Logging-Substrate" -o substrate.etl

# Start tracing
logman start SubstrateTrace

# Stop tracing
logman stop SubstrateTrace

# Convert to readable format
tracerpt substrate.etl
```

### Using PerfView

1. Download PerfView from Microsoft
2. Run: `PerfView.exe /onlyProviders=*Microsoft-M365-Core-Telemetry-R9-Logging-Substrate collect`
3. Reproduce the issue
4. Stop collection and analyze events

### Using EventListener (Programmatic)

```csharp
public class SubstrateEventListener : EventListener
{
    protected override void OnEventSourceCreated(EventSource eventSource)
    {
        if (eventSource.Name == "Microsoft-M365-Core-Telemetry-R9-Logging-Substrate")
        {
            EnableEvents(eventSource, EventLevel.Warning);
        }
    }

    protected override void OnEventWritten(EventWrittenEventArgs eventData)
    {
        Console.WriteLine($"[{eventData.Level}] {eventData.EventName}: {eventData.Payload?[0]}");
    }
}

// Register the listener
using var listener = new SubstrateEventListener();
```

## Event Categories and IDs

### Configuration Events (1-9)
- **1**: Configuration section not found
- **2**: Configuration binding failed  
- **3**: Required configuration missing
- **4**: Invalid configuration value

### ECS Configuration Events (10-19)
- **10**: ECS configuration section missing
- **11**: ECS configuration incomplete
- **12**: ECS authentication missing

### Exporter Events (20-29)
- **20**: Exporter configuration failed
- **21**: Composite exporter configuration not found
- **22**: ODL TCP configuration warning

### Enricher Events (30-39)
- **30**: Enricher registration failed

### General Events (100+)
- **100**: Substrate initialization started
- **101**: Substrate initialization completed
- **102**: Unexpected error

## Common Scenarios and Solutions

### Scenario 1: Missing Configuration Section

**Event**: `Configuration section 'SubstrateLogging:GenevaExporter' not found for GenevaLogExporterOptions`

**Solution**: 
```json
{
  "SubstrateLogging": {
    "GenevaExporter": {
      "ConnectionString": "EtwSession=YourSessionName",
      "TableNameMappings": {
        "*": "YourTableName"
      }
    }
  }
}
```

### Scenario 2: Invalid Connection String

**Event**: `Required configuration 'ConnectionString' missing for GenevaLogExporterOptions`

**Solution**: Provide a valid ETW session name:
```json
{
  "SubstrateLogging": {
    "GenevaExporter": {
      "ConnectionString": "EtwSession=MyAppSession"
    }
  }
}
```

### Scenario 3: ECS Configuration Issues

**Event**: `ECS configuration incomplete. Missing: Client,Agents,EnvironmentType`

**Solution**: Provide complete ECS configuration:
```json
{
  "ECSParameters": {
    "ECSIdentifiers": {
      "ServiceName": "MyService"
    },
    "Client": "MyClient",
    "Agents": ["MyAgent"],
    "EnvironmentType": "Production"
  }
}
```

## Best Practices

### For Service Owners

1. **Always use safe methods** - Use `AddEventSourceSubstrateLogging` instead of throwing variants
2. **Monitor EventSource events** - Set up monitoring for Warning and Error level events
3. **Test configuration** - Verify your configuration doesn't generate warnings
4. **Use fallbacks** - The substrate will apply safe defaults, but verify they meet your needs

### For Operations Teams

1. **Set up ETW collection** - Monitor substrate events in production
2. **Alert on errors** - Set up alerts for EventLevel.Error events
3. **Regular review** - Review Warning events to identify configuration drift
4. **Distributed tracing** - Correlate substrate events with application traces

### For Development Teams

1. **Use EventListener in tests** - Verify no configuration warnings in unit tests
2. **Mock configurations** - Test various configuration failure scenarios
3. **Validate defaults** - Ensure applied defaults are acceptable for your service
4. **Document requirements** - Clearly document required vs optional configuration

## Troubleshooting

### No Events Appearing

1. Verify EventSource name spelling
2. Check EventLevel filtering
3. Ensure the substrate is actually being initialized
4. Verify ETW session permissions (if using ETW)

### Too Many Events

1. Increase EventLevel filter (e.g., Warning instead of Informational)
2. Use specific event ID filtering
3. Check for configuration loops or repeated initialization

### Performance Impact

EventSource has minimal performance impact when:
- Events are not being collected
- Appropriate EventLevel filtering is used
- String formatting is kept simple

The substrate EventSources are designed to be always-on safe.
