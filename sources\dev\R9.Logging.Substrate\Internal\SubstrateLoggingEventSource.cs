// <copyright file="SubstrateLoggingEventSource.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.Tracing;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Internal
{
    /// <summary>
    /// EventSource for R9 Logging Substrate internal diagnostics
    /// This captures configuration errors and warnings without affecting customer services
    /// Controlled by ECS configuration - disabled by default for customer privacy
    /// </summary>
    [EventSource(Name = "Microsoft-M365-Core-Telemetry-R9-Logging-Substrate")]
    internal sealed class SubstrateLoggingEventSource : EventSource
    {
        /// <summary>
        /// The singleton instance of the event source
        /// </summary>
        public static readonly SubstrateLoggingEventSource Log = new SubstrateLoggingEventSource();

        private static bool _isEcsEnabled = false;
        private static bool _isEcsChecked = false;
        private static readonly object _lockObject = new object();

        private SubstrateLoggingEventSource() : base(EventSourceSettings.EtwSelfDescribingEventFormat)
        {
        }

        /// <summary>
        /// Check if EventSource should be enabled based on ECS configuration
        /// </summary>
        private bool ShouldLog(EventLevel level)
        {
            if (!IsEnabled(level, EventKeywords.None))
                return false;

            // Check ECS configuration once
            if (!_isEcsChecked)
            {
                lock (_lockObject)
                {
                    if (!_isEcsChecked)
                    {
                        _isEcsEnabled = CheckEcsEventSourceEnabled();
                        _isEcsChecked = true;
                    }
                }
            }

            return _isEcsEnabled;
        }

        /// <summary>
        /// Check ECS configuration to determine if EventSource should be enabled
        /// </summary>
        private static bool CheckEcsEventSourceEnabled()
        {
            try
            {
                // Check environment variable first (for testing/debugging)
                var envVar = Environment.GetEnvironmentVariable("R9_SUBSTRATE_EVENTSOURCE_ENABLED");
                if (bool.TryParse(envVar, out var envEnabled))
                {
                    return envEnabled;
                }

                // Check ECS configuration
                // This would integrate with your ECS configuration system
                // For now, default to disabled for customer privacy
                return false;
            }
            catch
            {
                // If ECS check fails, default to disabled
                return false;
            }
        }

        #region Configuration Events

        /// <summary>
        /// Configuration section not found, using defaults
        /// </summary>
        /// <param name="sectionName">The configuration section name</param>
        /// <param name="optionType">The option type name</param>
        [Event(1, Level = EventLevel.Warning, Message = "Configuration section '{0}' not found for {1}. Using default values.")]
        public void ConfigurationSectionNotFound(string sectionName, string optionType)
        {
            if (ShouldLog(EventLevel.Warning))
            {
                WriteEvent(1, sectionName ?? string.Empty, optionType ?? string.Empty);
            }
        }

        /// <summary>
        /// Configuration binding failed
        /// </summary>
        /// <param name="optionType">The option type name</param>
        /// <param name="error">The error message</param>
        [Event(2, Level = EventLevel.Error, Message = "Failed to bind configuration for {0}: {1}")]
        public void ConfigurationBindingFailed(string optionType, string error)
        {
            if (ShouldLog(EventLevel.Error))
            {
                WriteEvent(2, optionType ?? string.Empty, error ?? string.Empty);
            }
        }

        /// <summary>
        /// Required configuration value is missing
        /// </summary>
        /// <param name="propertyName">The property name</param>
        /// <param name="optionType">The option type name</param>
        /// <param name="defaultValue">The default value applied</param>
        [Event(3, Level = EventLevel.Warning, Message = "Required configuration '{0}' missing for {1}. Applied default: '{2}'")]
        public void RequiredConfigurationMissing(string propertyName, string optionType, string defaultValue)
        {
            if (IsEnabled(EventLevel.Warning, EventKeywords.None))
            {
                WriteEvent(3, propertyName ?? string.Empty, optionType ?? string.Empty, defaultValue ?? string.Empty);
            }
        }

        /// <summary>
        /// Invalid configuration value detected
        /// </summary>
        /// <param name="propertyName">The property name</param>
        /// <param name="invalidValue">The invalid value</param>
        /// <param name="validValues">The valid values</param>
        /// <param name="defaultValue">The default value applied</param>
        [Event(4, Level = EventLevel.Warning, Message = "Invalid configuration '{0}' value '{1}'. Valid values: {2}. Applied default: '{3}'")]
        public void InvalidConfigurationValue(string propertyName, string invalidValue, string validValues, string defaultValue)
        {
            if (IsEnabled(EventLevel.Warning, EventKeywords.None))
            {
                WriteEvent(4, propertyName ?? string.Empty, invalidValue ?? string.Empty, validValues ?? string.Empty, defaultValue ?? string.Empty);
            }
        }

        #endregion

        #region ECS Configuration Events

        /// <summary>
        /// ECS configuration section missing
        /// </summary>
        /// <param name="ecsType">The ECS type being used as fallback</param>
        [Event(10, Level = EventLevel.Warning, Message = "ECS configuration section missing. Using fallback: {0}")]
        public void EcsConfigurationMissing(string ecsType)
        {
            if (IsEnabled(EventLevel.Warning, EventKeywords.None))
            {
                WriteEvent(10, ecsType ?? string.Empty);
            }
        }

        /// <summary>
        /// ECS configuration incomplete
        /// </summary>
        /// <param name="missingFields">The missing fields</param>
        /// <param name="ecsType">The ECS type being used as fallback</param>
        [Event(11, Level = EventLevel.Warning, Message = "ECS configuration incomplete. Missing: {0}. Using fallback: {1}")]
        public void EcsConfigurationIncomplete(string missingFields, string ecsType)
        {
            if (IsEnabled(EventLevel.Warning, EventKeywords.None))
            {
                WriteEvent(11, missingFields ?? string.Empty, ecsType ?? string.Empty);
            }
        }

        /// <summary>
        /// ECS authentication missing for custom configuration
        /// </summary>
        /// <param name="ecsType">The ECS type being used as fallback</param>
        [Event(12, Level = EventLevel.Warning, Message = "ECS custom configuration detected but authentication missing. Using fallback: {0}")]
        public void EcsAuthenticationMissing(string ecsType)
        {
            if (IsEnabled(EventLevel.Warning, EventKeywords.None))
            {
                WriteEvent(12, ecsType ?? string.Empty);
            }
        }

        #endregion

        #region Exporter Events

        /// <summary>
        /// Exporter configuration failed
        /// </summary>
        /// <param name="exporterType">The exporter type</param>
        /// <param name="error">The error message</param>
        /// <param name="fallbackAction">The fallback action taken</param>
        [Event(20, Level = EventLevel.Error, Message = "Exporter '{0}' configuration failed: {1}. Fallback: {2}")]
        public void ExporterConfigurationFailed(string exporterType, string error, string fallbackAction)
        {
            if (IsEnabled(EventLevel.Error, EventKeywords.None))
            {
                WriteEvent(20, exporterType ?? string.Empty, error ?? string.Empty, fallbackAction ?? string.Empty);
            }
        }

        /// <summary>
        /// Composite exporter configuration not found
        /// </summary>
        /// <param name="fallbackExporter">The fallback exporter being used</param>
        [Event(21, Level = EventLevel.Warning, Message = "Composite exporter configuration not found. Using fallback: {0}")]
        public void CompositeExporterConfigurationNotFound(string fallbackExporter)
        {
            if (IsEnabled(EventLevel.Warning, EventKeywords.None))
            {
                WriteEvent(21, fallbackExporter ?? string.Empty);
            }
        }

        /// <summary>
        /// ODL TCP configuration warning
        /// </summary>
        /// <param name="warning">The warning message</param>
        [Event(22, Level = EventLevel.Warning, Message = "ODL TCP configuration warning: {0}")]
        public void OdlTcpConfigurationWarning(string warning)
        {
            if (IsEnabled(EventLevel.Warning, EventKeywords.None))
            {
                WriteEvent(22, warning ?? string.Empty);
            }
        }

        #endregion

        #region Enricher Events

        /// <summary>
        /// Enricher registration failed
        /// </summary>
        /// <param name="enricherType">The enricher type</param>
        /// <param name="error">The error message</param>
        [Event(30, Level = EventLevel.Warning, Message = "Failed to register enricher '{0}': {1}")]
        public void EnricherRegistrationFailed(string enricherType, string error)
        {
            if (IsEnabled(EventLevel.Warning, EventKeywords.None))
            {
                WriteEvent(30, enricherType ?? string.Empty, error ?? string.Empty);
            }
        }

        #endregion

        #region General Events

        /// <summary>
        /// Substrate logging initialization started
        /// </summary>
        /// <param name="version">The substrate version</param>
        [Event(100, Level = EventLevel.Informational, Message = "R9 Logging Substrate initialization started. Version: {0}")]
        public void SubstrateInitializationStarted(string version)
        {
            if (IsEnabled(EventLevel.Informational, EventKeywords.None))
            {
                WriteEvent(100, version ?? string.Empty);
            }
        }

        /// <summary>
        /// Substrate logging initialization completed
        /// </summary>
        /// <param name="configurationIssues">Number of configuration issues encountered</param>
        /// <param name="status">The final status</param>
        [Event(101, Level = EventLevel.Informational, Message = "R9 Logging Substrate initialization completed. Issues: {0}, Status: {1}")]
        public void SubstrateInitializationCompleted(int configurationIssues, string status)
        {
            if (IsEnabled(EventLevel.Informational, EventKeywords.None))
            {
                WriteEvent(101, configurationIssues, status ?? string.Empty);
            }
        }

        /// <summary>
        /// Unexpected error during substrate operation
        /// </summary>
        /// <param name="operation">The operation that failed</param>
        /// <param name="error">The error message</param>
        [Event(102, Level = EventLevel.Error, Message = "Unexpected error during {0}: {1}")]
        public void UnexpectedError(string operation, string error)
        {
            if (IsEnabled(EventLevel.Error, EventKeywords.None))
            {
                WriteEvent(102, operation ?? string.Empty, error ?? string.Empty);
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Safely logs validation result errors
        /// </summary>
        /// <param name="context">The context where validation failed</param>
        /// <param name="result">The validation result</param>
        public void LogValidationResult(string context, ValidationResult result)
        {
            if (!result.IsSuccess && IsEnabled())
            {
                foreach (var error in result.Errors)
                {
                    UnexpectedError(context, error);
                }
            }
        }

        /// <summary>
        /// Safely logs an exception without throwing
        /// </summary>
        /// <param name="operation">The operation that failed</param>
        /// <param name="exception">The exception</param>
        public void LogException(string operation, Exception exception)
        {
            try
            {
                if (IsEnabled(EventLevel.Error, EventKeywords.None))
                {
                    UnexpectedError(operation, exception?.ToString() ?? "Unknown error");
                }
            }
            catch
            {
                // Never throw from logging - this is critical for substrate reliability
            }
        }

        #endregion
    }
}
