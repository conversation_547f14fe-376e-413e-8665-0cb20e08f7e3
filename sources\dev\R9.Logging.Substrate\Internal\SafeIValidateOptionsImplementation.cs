// <copyright file="SafeIValidateOptionsImplementation.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using Microsoft.Extensions.Options;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Internal
{
    /// <summary>
    /// Safe IValidateOptions implementation for LoggingOptions that applies defaults instead of throwing
    /// </summary>
    public class SafeLoggingOptionsValidator : IValidateOptions<LoggingOptions>
    {
        public ValidateOptionsResult Validate(string? name, LoggingOptions options)
        {
            var builder = new ValidateOptionsResultBuilder();

            try
            {
                // Instead of failing validation, we apply safe defaults
                // and report what we fixed via EventSource
                
                // Example validation - add specific LoggingOptions validation here
                // For now, LoggingOptions doesn't have strict requirements
                
                // Always return success since we apply defaults
                return ValidateOptionsResult.Success;
            }
            catch (Exception ex)
            {
                // Even in validators, we avoid throwing exceptions
                // Apply defaults and log via EventSource
                SubstrateLoggingEventSource.Log.LogException("LoggingOptionsValidation", ex);
                
                // Return success since we'll apply defaults
                return ValidateOptionsResult.Success;
            }
        }
    }

    /// <summary>
    /// Safe IValidateOptions implementation for GenevaLogExporterOptions that applies defaults instead of throwing
    /// </summary>
    public class SafeGenevaLogExporterOptionsValidator : IValidateOptions<GenevaLogExporterOptions>
    {
        public ValidateOptionsResult Validate(string? name, GenevaLogExporterOptions options)
        {
            var builder = new ValidateOptionsResultBuilder();

            try
            {
                bool hasIssues = false;

                // Check ConnectionString
                if (string.IsNullOrEmpty(options.ConnectionString))
                {
                    options.ConnectionString = "EtwSession=DisabledSession";
                    SubstrateLoggingEventSource.Log.RequiredConfigurationMissing("ConnectionString", "GenevaLogExporterOptions", "EtwSession=DisabledSession");
                    hasIssues = true;
                }

                // Check TableNameMappings
                if (options.TableNameMappings == null || options.TableNameMappings.Count == 0)
                {
                    options.TableNameMappings = new System.Collections.Generic.Dictionary<string, string> { ["*"] = "DefaultTable" };
                    SubstrateLoggingEventSource.Log.RequiredConfigurationMissing("TableNameMappings", "GenevaLogExporterOptions", "DefaultTable");
                    hasIssues = true;
                }

                // We could add warnings to the result, but still return success
                // since we've applied defaults and the service can continue
                if (hasIssues)
                {
                    builder.AddResult("Configuration issues detected and resolved with defaults. Check EventSource for details.");
                }

                return builder.Build();
            }
            catch (Exception ex)
            {
                // Apply safe defaults even when validation itself fails
                try
                {
                    if (string.IsNullOrEmpty(options.ConnectionString))
                        options.ConnectionString = "EtwSession=DisabledSession";
                    
                    if (options.TableNameMappings == null || options.TableNameMappings.Count == 0)
                        options.TableNameMappings = new System.Collections.Generic.Dictionary<string, string> { ["*"] = "DefaultTable" };
                }
                catch
                {
                    // If even applying defaults fails, we'll let the framework handle it
                }

                SubstrateLoggingEventSource.Log.LogException("GenevaLogExporterOptionsValidation", ex);
                
                // Return success with applied defaults
                builder.AddResult($"Validation error occurred but defaults applied: {ex.Message}");
                return builder.Build();
            }
        }
    }

    /// <summary>
    /// Extension methods for registering safe IValidateOptions implementations
    /// </summary>
    public static class SafeValidateOptionsExtensions
    {
        /// <summary>
        /// Registers safe IValidateOptions validators that apply defaults instead of throwing
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection</returns>
        public static IServiceCollection AddSafeValidateOptions(this IServiceCollection services)
        {
            // Register safe validators
            services.AddSingleton<IValidateOptions<LoggingOptions>, SafeLoggingOptionsValidator>();
            services.AddSingleton<IValidateOptions<GenevaLogExporterOptions>, SafeGenevaLogExporterOptionsValidator>();
            
            return services;
        }

        /// <summary>
        /// Configures options with safe validation
        /// </summary>
        /// <typeparam name="TOptions">The options type</typeparam>
        /// <param name="services">The service collection</param>
        /// <param name="configurationSection">The configuration section</param>
        /// <returns>The service collection</returns>
        public static IServiceCollection ConfigureWithSafeValidation<TOptions>(
            this IServiceCollection services, 
            IConfigurationSection configurationSection) 
            where TOptions : class
        {
            // Configure the options
            services.Configure<TOptions>(configurationSection);
            
            // The safe validators are already registered and will be used automatically
            // They apply defaults instead of throwing exceptions
            
            return services;
        }
    }
}

namespace Microsoft.Extensions.DependencyInjection
{
    /// <summary>
    /// Extension methods for safe options access
    /// </summary>
    public static class SafeOptionsAccessExtensions
    {
        /// <summary>
        /// Gets options value safely, applying defaults if validation fails
        /// </summary>
        /// <typeparam name="TOptions">The options type</typeparam>
        /// <param name="serviceProvider">The service provider</param>
        /// <param name="fallbackOptions">Fallback options if validation fails</param>
        /// <returns>The options value (never null)</returns>
        public static TOptions GetOptionsSafely<TOptions>(
            this IServiceProvider serviceProvider, 
            TOptions? fallbackOptions = null) 
            where TOptions : class, new()
        {
            try
            {
                var options = serviceProvider.GetRequiredService<IOptions<TOptions>>();
                return options.Value; // Our safe validators ensure this won't throw
            }
            catch (OptionsValidationException)
            {
                // If somehow validation still fails, use fallback
                SubstrateLoggingEventSource.Log.UnexpectedError("GetOptionsSafely", $"Options validation failed for {typeof(TOptions).Name}, using fallback");
                return fallbackOptions ?? new TOptions();
            }
            catch (Exception ex)
            {
                // For any other errors, use fallback
                SubstrateLoggingEventSource.Log.LogException("GetOptionsSafely", ex);
                return fallbackOptions ?? new TOptions();
            }
        }
    }
}
