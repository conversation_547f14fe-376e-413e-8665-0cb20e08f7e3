// <copyright file="SubstrateMeteringEventSource.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics.Tracing;

namespace Microsoft.M365.Core.Telemetry.R9.Metering.Substrate.Internal
{
    /// <summary>
    /// EventSource for R9 Metering Substrate internal diagnostics
    /// This captures configuration errors and warnings without affecting customer services
    /// </summary>
    [EventSource(Name = "Microsoft-M365-Core-Telemetry-R9-Metering-Substrate")]
    internal sealed class SubstrateMeteringEventSource : EventSource
    {
        /// <summary>
        /// The singleton instance of the event source
        /// </summary>
        public static readonly SubstrateMeteringEventSource Log = new SubstrateMeteringEventSource();

        private SubstrateMeteringEventSource() : base(EventSourceSettings.EtwSelfDescribingEventFormat)
        {
        }

        #region Configuration Events

        /// <summary>
        /// Configuration section not found, using defaults
        /// </summary>
        /// <param name="sectionName">The configuration section name</param>
        /// <param name="optionType">The option type name</param>
        [Event(1, Level = EventLevel.Warning, Message = "Configuration section '{0}' not found for {1}. Using default values.")]
        public void ConfigurationSectionNotFound(string sectionName, string optionType)
        {
            if (IsEnabled(EventLevel.Warning, EventKeywords.None))
            {
                WriteEvent(1, sectionName ?? string.Empty, optionType ?? string.Empty);
            }
        }

        /// <summary>
        /// Configuration binding failed
        /// </summary>
        /// <param name="optionType">The option type name</param>
        /// <param name="error">The error message</param>
        [Event(2, Level = EventLevel.Error, Message = "Failed to bind configuration for {0}: {1}")]
        public void ConfigurationBindingFailed(string optionType, string error)
        {
            if (IsEnabled(EventLevel.Error, EventKeywords.None))
            {
                WriteEvent(2, optionType ?? string.Empty, error ?? string.Empty);
            }
        }

        /// <summary>
        /// Required configuration value is missing
        /// </summary>
        /// <param name="propertyName">The property name</param>
        /// <param name="optionType">The option type name</param>
        /// <param name="defaultValue">The default value applied</param>
        [Event(3, Level = EventLevel.Warning, Message = "Required configuration '{0}' missing for {1}. Applied default: '{2}'")]
        public void RequiredConfigurationMissing(string propertyName, string optionType, string defaultValue)
        {
            if (IsEnabled(EventLevel.Warning, EventKeywords.None))
            {
                WriteEvent(3, propertyName ?? string.Empty, optionType ?? string.Empty, defaultValue ?? string.Empty);
            }
        }

        /// <summary>
        /// Invalid configuration value detected
        /// </summary>
        /// <param name="propertyName">The property name</param>
        /// <param name="invalidValue">The invalid value</param>
        /// <param name="validValues">The valid values</param>
        /// <param name="defaultValue">The default value applied</param>
        [Event(4, Level = EventLevel.Warning, Message = "Invalid configuration '{0}' value '{1}'. Valid values: {2}. Applied default: '{3}'")]
        public void InvalidConfigurationValue(string propertyName, string invalidValue, string validValues, string defaultValue)
        {
            if (IsEnabled(EventLevel.Warning, EventKeywords.None))
            {
                WriteEvent(4, propertyName ?? string.Empty, invalidValue ?? string.Empty, validValues ?? string.Empty, defaultValue ?? string.Empty);
            }
        }

        #endregion

        #region Metering-Specific Events

        /// <summary>
        /// Meter state overrides are empty
        /// </summary>
        /// <param name="defaultState">The default state applied</param>
        [Event(10, Level = EventLevel.Warning, Message = "MeterStateOverrides is empty. Metrics will be disabled. Applied default state: {0}")]
        public void MeterStateOverridesEmpty(string defaultState)
        {
            if (IsEnabled(EventLevel.Warning, EventKeywords.None))
            {
                WriteEvent(10, defaultState ?? string.Empty);
            }
        }

        /// <summary>
        /// Geneva monitoring account missing
        /// </summary>
        /// <param name="defaultAccount">The default account applied</param>
        [Event(11, Level = EventLevel.Warning, Message = "MonitoringAccount is required for Geneva Metrics. Applied default: '{0}'")]
        public void MonitoringAccountMissing(string defaultAccount)
        {
            if (IsEnabled(EventLevel.Warning, EventKeywords.None))
            {
                WriteEvent(11, defaultAccount ?? string.Empty);
            }
        }

        /// <summary>
        /// Geneva monitoring namespace missing
        /// </summary>
        /// <param name="defaultNamespace">The default namespace applied</param>
        [Event(12, Level = EventLevel.Warning, Message = "MonitoringNamespace is required for Geneva Metrics. Applied default: '{0}'")]
        public void MonitoringNamespaceMissing(string defaultNamespace)
        {
            if (IsEnabled(EventLevel.Warning, EventKeywords.None))
            {
                WriteEvent(12, defaultNamespace ?? string.Empty);
            }
        }

        /// <summary>
        /// Duplicate Geneva exporter detected
        /// </summary>
        [Event(13, Level = EventLevel.Warning, Message = "Geneva Exporter is already added for metering. This may cause data duplication.")]
        public void DuplicateGenevaExporterDetected()
        {
            if (IsEnabled(EventLevel.Warning, EventKeywords.None))
            {
                WriteEvent(13);
            }
        }

        #endregion

        #region Exporter Events

        /// <summary>
        /// Geneva exporter configuration failed
        /// </summary>
        /// <param name="error">The error message</param>
        /// <param name="fallbackAction">The fallback action taken</param>
        [Event(20, Level = EventLevel.Error, Message = "Geneva exporter configuration failed: {0}. Fallback: {1}")]
        public void GenevaExporterConfigurationFailed(string error, string fallbackAction)
        {
            if (IsEnabled(EventLevel.Error, EventKeywords.None))
            {
                WriteEvent(20, error ?? string.Empty, fallbackAction ?? string.Empty);
            }
        }

        /// <summary>
        /// Metering options configuration failed
        /// </summary>
        /// <param name="error">The error message</param>
        /// <param name="fallbackAction">The fallback action taken</param>
        [Event(21, Level = EventLevel.Error, Message = "Metering options configuration failed: {0}. Fallback: {1}")]
        public void MeteringOptionsConfigurationFailed(string error, string fallbackAction)
        {
            if (IsEnabled(EventLevel.Error, EventKeywords.None))
            {
                WriteEvent(21, error ?? string.Empty, fallbackAction ?? string.Empty);
            }
        }

        #endregion

        #region Enricher Events

        /// <summary>
        /// Metric enricher registration failed
        /// </summary>
        /// <param name="enricherType">The enricher type</param>
        /// <param name="error">The error message</param>
        [Event(30, Level = EventLevel.Warning, Message = "Failed to register metric enricher '{0}': {1}")]
        public void MetricEnricherRegistrationFailed(string enricherType, string error)
        {
            if (IsEnabled(EventLevel.Warning, EventKeywords.None))
            {
                WriteEvent(30, enricherType ?? string.Empty, error ?? string.Empty);
            }
        }

        #endregion

        #region General Events

        /// <summary>
        /// Substrate metering initialization started
        /// </summary>
        /// <param name="version">The substrate version</param>
        [Event(100, Level = EventLevel.Informational, Message = "R9 Metering Substrate initialization started. Version: {0}")]
        public void SubstrateInitializationStarted(string version)
        {
            if (IsEnabled(EventLevel.Informational, EventKeywords.None))
            {
                WriteEvent(100, version ?? string.Empty);
            }
        }

        /// <summary>
        /// Substrate metering initialization completed
        /// </summary>
        /// <param name="configurationIssues">Number of configuration issues encountered</param>
        /// <param name="status">The final status</param>
        [Event(101, Level = EventLevel.Informational, Message = "R9 Metering Substrate initialization completed. Issues: {0}, Status: {1}")]
        public void SubstrateInitializationCompleted(int configurationIssues, string status)
        {
            if (IsEnabled(EventLevel.Informational, EventKeywords.None))
            {
                WriteEvent(101, configurationIssues, status ?? string.Empty);
            }
        }

        /// <summary>
        /// Unexpected error during substrate operation
        /// </summary>
        /// <param name="operation">The operation that failed</param>
        /// <param name="error">The error message</param>
        [Event(102, Level = EventLevel.Error, Message = "Unexpected error during {0}: {1}")]
        public void UnexpectedError(string operation, string error)
        {
            if (IsEnabled(EventLevel.Error, EventKeywords.None))
            {
                WriteEvent(102, operation ?? string.Empty, error ?? string.Empty);
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Safely logs validation result errors
        /// </summary>
        /// <param name="context">The context where validation failed</param>
        /// <param name="result">The validation result</param>
        public void LogValidationResult(string context, ValidationResult result)
        {
            if (!result.IsSuccess && IsEnabled())
            {
                foreach (var error in result.Errors)
                {
                    UnexpectedError(context, error);
                }
            }
        }

        /// <summary>
        /// Safely logs an exception without throwing
        /// </summary>
        /// <param name="operation">The operation that failed</param>
        /// <param name="exception">The exception</param>
        public void LogException(string operation, Exception exception)
        {
            try
            {
                if (IsEnabled(EventLevel.Error, EventKeywords.None))
                {
                    UnexpectedError(operation, exception?.ToString() ?? "Unknown error");
                }
            }
            catch
            {
                // Never throw from logging - this is critical for substrate reliability
            }
        }

        #endregion
    }
}
