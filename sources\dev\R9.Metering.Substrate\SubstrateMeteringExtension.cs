﻿// <copyright file="SubstrateMeteringExtension.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Core.Telemetry.R9.Metering.Substrate.Internal;
using Microsoft.R9.Extensions.Enrichment;
using Microsoft.R9.Extensions.Metering;
using Microsoft.R9.Extensions.Metering.Exporters;
using OpenTelemetry;
using OpenTelemetry.Metrics;

namespace Microsoft.M365.Core.Telemetry.R9.Metering.Substrate
{
    /// <summary>
    /// Extension methods for Substrate metering
    /// </summary>
    public static class SubstrateMeteringExtension
    {
        /// <summary>
        /// Configures Substrate metering using the provided MeterProviderBuilder and service configuration.
        /// </summary>
        /// <param name="meterProviderBuilder">The MeterProviderBuilder to configure.</param>
        /// <param name="serviceConfiguration">The service configuration to use for metering options.</param>
        /// <param name="onValidationFailure">Optional callback for validation failures (for advanced diagnostics)</param>
        /// <returns>The configured MeterProviderBuilder.</returns>
        public static MeterProviderBuilder ConfigureSubstrateMetering(
            this MeterProviderBuilder meterProviderBuilder,
            IConfiguration serviceConfiguration,
            Action<ValidationResult>? onValidationFailure = null)
        {
            var version = GetSubstrateVersion();
            SubstrateMeteringEventSource.Log.SubstrateInitializationStarted(version);

            var issueCount = 0;
            var status = "Success";

            try
            {
                var result = meterProviderBuilder.ConfigureSubstrateMeteringSafe(serviceConfiguration);
                if (!result.IsSuccess)
                {
                    issueCount = result.Errors.Count;
                    status = "CompletedWithIssues";
                    SubstrateMeteringEventSource.Log.LogValidationResult("SubstrateMeteringConfiguration", result);

                    // Call user callback if provided
                    onValidationFailure?.Invoke(result);
                }
            }
            catch (Exception ex)
            {
                issueCount = 1;
                status = "Failed";
                SubstrateMeteringEventSource.Log.LogException("ConfigureSubstrateMetering", ex);

                // Don't rethrow - the safe implementation should handle this
                // but if it doesn't, we continue with minimal configuration
            }
            finally
            {
                SubstrateMeteringEventSource.Log.SubstrateInitializationCompleted(issueCount, status);
            }

            return meterProviderBuilder;
        }

        /// <summary>
        /// Add metering for Substrate services
        /// </summary>
        /// <param name="serviceCollection">The service collection</param>
        /// <param name="serviceConfiguration">The service configuration</param>
        /// <param name="configure">Optional configuration action for meter provider builder</param>
        /// <param name="onValidationFailure">Optional callback for validation failures (for advanced diagnostics)</param>
        /// <returns>The service collection</returns>
        public static IServiceCollection AddSubstrateMetering(
            this IServiceCollection serviceCollection,
            IConfiguration serviceConfiguration,
            Action<MeterProviderBuilder>? configure = null,
            Action<ValidationResult>? onValidationFailure = null)
        {
            serviceCollection.AddOpenTelemetry().WithMetrics(meterProviderBuilder =>
            {
                // Load user customized actions for meter provider builder if there is
                configure?.Invoke(meterProviderBuilder);

                ConfigureSubstrateMetering(meterProviderBuilder, serviceConfiguration, onValidationFailure);
            });

            return serviceCollection;
        }

        /// <summary>
        /// Configures Substrate metering using the provided service configuration and optional MeterProviderBuilder configuration.
        /// </summary>
        /// <param name="serviceConfiguration">The service configuration to use for metering options.</param>
        /// <param name="configure">An optional action to configure the MeterProviderBuilder.</param>
        /// <param name="onValidationFailure">Optional callback for validation failures (for advanced diagnostics)</param>
        public static void ConfigureSubstrateMetering(
            IConfiguration serviceConfiguration,
            Action<MeterProviderBuilder>? configure = null,
            Action<ValidationResult>? onValidationFailure = null)
        {
            try
            {
                var meterBuilder = Sdk.CreateMeterProviderBuilder();
                configure?.Invoke(meterBuilder);
                meterBuilder.ConfigureSubstrateMetering(serviceConfiguration, onValidationFailure);

                var meterProvider = meterBuilder.Build();

                // Safe disposal setup
                try
                {
                    AppDomain.CurrentDomain.ProcessExit += (sender, args) =>
                    {
                        try
                        {
                            meterProvider?.Dispose();
                        }
                        catch
                        {
                            // Ignore disposal errors
                        }
                    };

                    AppDomain.CurrentDomain.UnhandledException += (sender, args) =>
                    {
                        try
                        {
                            meterProvider?.Dispose();
                        }
                        catch
                        {
                            // Ignore disposal errors
                        }
                    };
                }
                catch
                {
                    // Ignore event handler registration errors
                }
            }
            catch (Exception ex)
            {
                SubstrateMeteringEventSource.Log.LogException("ConfigureSubstrateMetering", ex);

                // Don't rethrow - this method should never crash customer services
                var result = ValidationResult.Failure($"Failed to configure substrate metering: {ex.Message}");
                onValidationFailure?.Invoke(result);
            }
        }

        /// <summary>
        /// Internal safe configuration method that doesn't throw exceptions
        /// </summary>
        internal static ValidationResult ConfigureSubstrateMeteringSafe(
            this MeterProviderBuilder meterProviderBuilder,
            IConfiguration serviceConfiguration)
        {
            var allResults = new List<ValidationResult>();

            // Check for duplicate Geneva exporters safely
            var duplicateCheckResult = SafeCheckForDuplicateGenevaExporter(meterProviderBuilder);
            allResults.Add(duplicateCheckResult);

            // Configure metering options safely
            var meteringResult = ConfigureMeteringOptionsSafe(meterProviderBuilder, serviceConfiguration);
            allResults.Add(meteringResult);

            // Configure Geneva exporter safely
            var genevaResult = ConfigureGenevaExporterSafe(meterProviderBuilder, serviceConfiguration);
            allResults.Add(genevaResult);

            // Add enrichers safely
            var enricherResult = ConfigureEnrichersSafe(meterProviderBuilder);
            allResults.Add(enricherResult);

            return ValidationResult.Combine(allResults.ToArray());
        }

        /// <summary>
        /// Safely check for duplicate Geneva exporters
        /// </summary>
        private static ValidationResult SafeCheckForDuplicateGenevaExporter(MeterProviderBuilder meterProviderBuilder)
        {
            try
            {
                meterProviderBuilder.ConfigureServices(services =>
                {
                    try
                    {
                        // Check for duplicate Geneva exporters
                        if (services.Any(s => s.ImplementationType?.Name?.EndsWith("GenevaMeteringExporterOptionsValidator", StringComparison.CurrentCulture) == true))
                        {
                            SubstrateMeteringEventSource.Log.DuplicateGenevaExporterDetected();
                        }
                    }
                    catch (Exception ex)
                    {
                        SubstrateMeteringEventSource.Log.LogException("DuplicateGenevaExporterCheck", ex);
                    }
                });
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                SubstrateMeteringEventSource.Log.LogException("ConfigureServicesForDuplicateCheck", ex);
                // Don't fail the entire configuration for this
                return ValidationResult.Success();
            }
        }

        /// <summary>
        /// Safely configure metering options
        /// </summary>
        private static ValidationResult ConfigureMeteringOptionsSafe(MeterProviderBuilder meterProviderBuilder, IConfiguration serviceConfiguration)
        {
            try
            {
                meterProviderBuilder.AddMetering(options =>
                {
                    var result = options.SafeUpdateAndValidateOptions(serviceConfiguration);
                    if (!result.IsSuccess)
                    {
                        SubstrateMeteringEventSource.Log.LogValidationResult("MeteringOptions", result);
                    }
                });
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                SubstrateMeteringEventSource.Log.MeteringOptionsConfigurationFailed(ex.Message, "Using defaults");
                return ValidationResult.Failure($"Failed to configure metering options: {ex.Message}");
            }
        }

        /// <summary>
        /// Safely configure Geneva exporter
        /// </summary>
        private static ValidationResult ConfigureGenevaExporterSafe(MeterProviderBuilder meterProviderBuilder, IConfiguration serviceConfiguration)
        {
            try
            {
                meterProviderBuilder.AddGenevaExporter(options =>
                {
                    var result = options.SafeUpdateAndValidateOptions(serviceConfiguration);
                    if (!result.IsSuccess)
                    {
                        SubstrateMeteringEventSource.Log.LogValidationResult("GenevaExporter", result);
                        options.ApplyDefaults();
                    }
                });
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                SubstrateMeteringEventSource.Log.GenevaExporterConfigurationFailed(ex.Message, "None");
                return ValidationResult.Failure($"Geneva exporter configuration failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Safely configure enrichers
        /// </summary>
        private static ValidationResult ConfigureEnrichersSafe(MeterProviderBuilder meterProviderBuilder)
        {
            try
            {
                meterProviderBuilder.ConfigureServices(services =>
                {
                    try
                    {
                        services.AddMetricEnricher<B2PassiveMetricEnricher>();
                    }
                    catch (Exception ex)
                    {
                        SubstrateMeteringEventSource.Log.MetricEnricherRegistrationFailed(nameof(B2PassiveMetricEnricher), ex.Message);
                    }
                });
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                SubstrateMeteringEventSource.Log.LogException("ConfigureEnrichers", ex);
                // Don't fail the entire configuration for this
                return ValidationResult.Success();
            }
        }

        /// <summary>
        /// Get the substrate version for diagnostics
        /// </summary>
        private static string GetSubstrateVersion()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return version?.ToString() ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }
    }
}
