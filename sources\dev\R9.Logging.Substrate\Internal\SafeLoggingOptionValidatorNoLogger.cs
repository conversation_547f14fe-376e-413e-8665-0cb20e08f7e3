// <copyright file="SafeLoggingOptionValidatorNoLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Internal
{
    /// <summary>
    /// Safe validation helper that doesn't throw exceptions or use ILogger
    /// </summary>
    internal static class SafeValidatorHelper
    {
        /// <summary>
        /// Mapping between option type and configuration section name
        /// </summary>
        private static readonly Dictionary<Type, string> configSectionName = new ()
        {
            [typeof(LoggingOptions)] = "SubstrateLogging:R9Logging",
            [typeof(GenevaLogExporterOptions)] = "SubstrateLogging:GenevaExporter"
        };

        /// <summary>
        /// Safely update options from configuration (general for all options)
        /// </summary>
        /// <typeparam name="T">option type</typeparam>
        /// <param name="options">The options to update</param>
        /// <param name="configuration">The configuration</param>
        /// <returns>Validation result</returns>
        internal static ValidationResult SafeUpdateOption<T>(this T options, IConfiguration configuration)
        {
            try
            {
                if (configSectionName.TryGetValue(typeof(T), out var sectionName))
                {
                    var section = configuration.GetSection(sectionName);
                    if (section.Exists())
                    {
                        section.Bind(options);
                        return ValidationResult.Success();
                    }
                    else
                    {
                        // Configuration section not found, use defaults - this is not an error
                        return ValidationResult.Success();
                    }
                }
                else
                {
                    var error = $"Option Type [{typeof(T)}] is not registered for configuration loading.";
                    return ValidationResult.Failure(error);
                }
            }
            catch (Exception ex)
            {
                var error = $"Failed to bind configuration for {typeof(T).Name}: {ex.Message}";
                return ValidationResult.Failure(error);
            }
        }
    }

    /// <summary>
    /// Safe validation for logging options without ILogger dependency
    /// </summary>
    public static class SafeLoggingOptionValidator
    {
        /// <summary>
        /// Safely load values from configuration section "Logging", update and validate logging options
        /// </summary>
        /// <param name="options">The logging options</param>
        /// <param name="configuration">The configuration</param>
        /// <returns>Validation result</returns>
        public static ValidationResult SafeUpdateAndValidateOptions(this LoggingOptions options, IConfiguration configuration)
        {
            var updateResult = options.SafeUpdateOption(configuration);
            if (!updateResult.IsSuccess)
            {
                return updateResult;
            }

            // Add any additional validation logic here if needed
            // For now, LoggingOptions doesn't have specific validation requirements
            
            return ValidationResult.Success();
        }
    }

    /// <summary>
    /// Safe validation for Geneva log exporter options without ILogger dependency
    /// </summary>
    public static class SafeGenevaLoggingOptionValidator
    {
        /// <summary>
        /// Safely load values from configuration "GenevaLoggingExporter", update and validate logging options
        /// </summary>
        /// <param name="options">The Geneva log exporter options</param>
        /// <param name="configuration">The configuration</param>
        /// <returns>Validation result</returns>
        public static ValidationResult SafeUpdateAndValidateOptions(this GenevaLogExporterOptions options, IConfiguration configuration)
        {
            var updateResult = options.SafeUpdateOption(configuration);
            if (!updateResult.IsSuccess)
            {
                return updateResult;
            }

            var errors = new List<string>();

            // Validate ConnectionString
            if (string.IsNullOrEmpty(options.ConnectionString))
            {
                errors.Add("ConnectionString is required for Geneva Event. Please provide a valid connection string in configuration.");
            }

            // Validate TableNameMappings
            if (options.TableNameMappings == null || options.TableNameMappings.Count == 0)
            {
                errors.Add("TableNameMappings is required for Geneva Event. Please provide table name mappings in configuration.");
            }

            if (errors.Count > 0)
            {
                // Apply defaults instead of failing
                ApplyDefaults(options);
                // Return success with applied defaults rather than failure
                return ValidationResult.Success();
            }

            return ValidationResult.Success();
        }

        /// <summary>
        /// Applies default values for Geneva logging options when configuration is missing
        /// </summary>
        /// <param name="options">The Geneva log exporter options</param>
        public static void ApplyDefaults(this GenevaLogExporterOptions options)
        {
            if (string.IsNullOrEmpty(options.ConnectionString))
            {
                // Set a safe default that won't crash but will be non-functional
                options.ConnectionString = "EtwSession=DisabledSession";
            }

            if (options.TableNameMappings == null || options.TableNameMappings.Count == 0)
            {
                options.TableNameMappings = new Dictionary<string, string>
                {
                    ["*"] = "DefaultTable"
                };
            }
        }
    }
}
