// <copyright file="SafeSubstrateMeteringExtensionNoLogger.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.M365.Core.Telemetry.Enrichment;
using Microsoft.M365.Core.Telemetry.R9.Metering.Substrate.Internal;
using Microsoft.R9.Extensions.Enrichment;
using Microsoft.R9.Extensions.Metering;
using Microsoft.R9.Extensions.Metering.Exporters;
using OpenTelemetry;
using OpenTelemetry.Metrics;

namespace Microsoft.M365.Core.Telemetry.R9.Metering.Substrate
{
    /// <summary>
    /// Safe extension methods for Substrate metering that don't throw exceptions or use ILogger.
    /// These methods provide graceful fallbacks when configuration is invalid.
    /// </summary>
    public static class SafeSubstrateMeteringExtension
    {
        /// <summary>
        /// Safely configures Substrate metering using the provided MeterProviderBuilder and service configuration.
        /// </summary>
        /// <param name="meterProviderBuilder">The MeterProviderBuilder to configure.</param>
        /// <param name="serviceConfiguration">The service configuration to use for metering options.</param>
        /// <param name="onValidationFailure">Optional callback for validation failures</param>
        /// <returns>Validation result indicating success or failure</returns>
        public static ValidationResult ConfigureSafeSubstrateMetering(
            this MeterProviderBuilder meterProviderBuilder, 
            IConfiguration serviceConfiguration,
            Action<ValidationResult>? onValidationFailure = null)
        {
            try
            {
                var allResults = new List<ValidationResult>();

                // Check for duplicate Geneva exporters safely
                var duplicateCheckResult = SafeCheckForDuplicateGenevaExporter(meterProviderBuilder);
                allResults.Add(duplicateCheckResult);

                // Configure metering options safely
                var meteringResult = ConfigureMeteringOptions(meterProviderBuilder, serviceConfiguration);
                allResults.Add(meteringResult);

                // Configure Geneva exporter safely
                var genevaResult = ConfigureGenevaExporter(meterProviderBuilder, serviceConfiguration);
                allResults.Add(genevaResult);

                // Add enrichers safely
                var enricherResult = ConfigureEnrichers(meterProviderBuilder);
                allResults.Add(enricherResult);

                var combinedResult = ValidationResult.Combine(allResults.ToArray());
                if (!combinedResult.IsSuccess)
                {
                    onValidationFailure?.Invoke(combinedResult);
                }

                return combinedResult;
            }
            catch (Exception ex)
            {
                var error = $"Unexpected error during safe substrate metering configuration: {ex.Message}";
                var result = ValidationResult.Failure(error);
                onValidationFailure?.Invoke(result);
                return result;
            }
        }

        /// <summary>
        /// Safely add metering for Substrate services
        /// </summary>
        /// <param name="serviceCollection">The service collection</param>
        /// <param name="serviceConfiguration">The service configuration</param>
        /// <param name="configure">Optional configuration action</param>
        /// <param name="onValidationFailure">Optional callback for validation failures</param>
        /// <returns>The service collection</returns>
        public static IServiceCollection AddSafeSubstrateMetering(
            this IServiceCollection serviceCollection,
            IConfiguration serviceConfiguration,
            Action<MeterProviderBuilder>? configure = null,
            Action<ValidationResult>? onValidationFailure = null)
        {
            serviceCollection.AddOpenTelemetry().WithMetrics(meterProviderBuilder =>
            {
                // Load user customized actions for meter provider builder if there is
                configure?.Invoke(meterProviderBuilder);

                var result = meterProviderBuilder.ConfigureSafeSubstrateMetering(serviceConfiguration);
                if (!result.IsSuccess)
                {
                    onValidationFailure?.Invoke(result);
                }
            });

            return serviceCollection;
        }

        /// <summary>
        /// Safely configures Substrate metering using the provided service configuration and optional MeterProviderBuilder configuration.
        /// </summary>
        /// <param name="serviceConfiguration">The service configuration to use for metering options.</param>
        /// <param name="configure">An optional action to configure the MeterProviderBuilder.</param>
        /// <param name="onValidationFailure">Optional callback for validation failures</param>
        /// <returns>Validation result indicating success or failure</returns>
        public static ValidationResult ConfigureSafeSubstrateMetering(
            IConfiguration serviceConfiguration, 
            Action<MeterProviderBuilder>? configure = null,
            Action<ValidationResult>? onValidationFailure = null)
        {
            try
            {
                var meterBuilder = Sdk.CreateMeterProviderBuilder();
                configure?.Invoke(meterBuilder);
                
                var result = meterBuilder.ConfigureSafeSubstrateMetering(serviceConfiguration, onValidationFailure);

                var meterProvider = meterBuilder.Build();
                
                // Safe disposal setup
                try
                {
                    AppDomain.CurrentDomain.ProcessExit += (sender, args) =>
                    {
                        try
                        {
                            meterProvider?.Dispose();
                        }
                        catch
                        {
                            // Ignore disposal errors
                        }
                    };
                    
                    AppDomain.CurrentDomain.UnhandledException += (sender, args) =>
                    {
                        try
                        {
                            meterProvider?.Dispose();
                        }
                        catch
                        {
                            // Ignore disposal errors
                        }
                    };
                }
                catch
                {
                    // Ignore event handler registration errors
                }

                return result;
            }
            catch (Exception ex)
            {
                var error = $"Failed to configure safe substrate metering: {ex.Message}";
                var result = ValidationResult.Failure(error);
                onValidationFailure?.Invoke(result);
                return result;
            }
        }

        /// <summary>
        /// Safely check for duplicate Geneva exporters
        /// </summary>
        private static ValidationResult SafeCheckForDuplicateGenevaExporter(MeterProviderBuilder meterProviderBuilder)
        {
            try
            {
                meterProviderBuilder.ConfigureServices(services =>
                {
                    try
                    {
                        // Check for duplicate Geneva exporters
                        if (services.Any(s => s.ImplementationType?.Name?.EndsWith("GenevaMeteringExporterOptionsValidator", StringComparison.CurrentCulture) == true))
                        {
                            // Don't throw, just note the issue - this is handled gracefully
                        }
                    }
                    catch
                    {
                        // Ignore errors in duplicate check
                    }
                });
                return ValidationResult.Success();
            }
            catch
            {
                // Don't fail the entire configuration for this
                return ValidationResult.Success();
            }
        }

        /// <summary>
        /// Safely configure metering options
        /// </summary>
        private static ValidationResult ConfigureMeteringOptions(MeterProviderBuilder meterProviderBuilder, IConfiguration serviceConfiguration)
        {
            try
            {
                meterProviderBuilder.AddMetering(options =>
                {
                    var result = options.SafeUpdateAndValidateOptions(serviceConfiguration);
                    // Note: We don't fail here even if validation fails, as defaults are applied
                });
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                var error = $"Failed to configure metering options: {ex.Message}";
                return ValidationResult.Failure(error);
            }
        }

        /// <summary>
        /// Safely configure Geneva exporter
        /// </summary>
        private static ValidationResult ConfigureGenevaExporter(MeterProviderBuilder meterProviderBuilder, IConfiguration serviceConfiguration)
        {
            try
            {
                meterProviderBuilder.AddGenevaExporter(options =>
                {
                    var result = options.SafeUpdateAndValidateOptions(serviceConfiguration);
                    if (!result.IsSuccess)
                    {
                        // Apply defaults when validation fails
                        options.ApplyDefaults();
                    }
                });
                return ValidationResult.Success();
            }
            catch (Exception ex)
            {
                var error = $"Failed to configure Geneva exporter: {ex.Message}";
                return ValidationResult.Failure(error);
            }
        }

        /// <summary>
        /// Safely configure enrichers
        /// </summary>
        private static ValidationResult ConfigureEnrichers(MeterProviderBuilder meterProviderBuilder)
        {
            try
            {
                meterProviderBuilder.ConfigureServices(services =>
                {
                    try
                    {
                        services.AddMetricEnricher<B2PassiveMetricEnricher>();
                    }
                    catch
                    {
                        // Ignore enricher failures
                    }
                });
                return ValidationResult.Success();
            }
            catch
            {
                // Don't fail the entire configuration for this
                return ValidationResult.Success();
            }
        }
    }
}
