# ECS-Controlled EventSource Diagnostics

## Overview

The R9 Substrate extensions now use EventSource for internal diagnostics, controlled by ECS configuration. This provides:

- **Customer Privacy** - Diagnostics disabled by default
- **ECS Control** - Centrally managed via ECS configuration
- **Zero Impact** - No performance or reliability impact when disabled
- **Rich Diagnostics** - Detailed telemetry when enabled for troubleshooting

## Default Behavior

**EventSource is DISABLED by default** to protect customer privacy. No diagnostic events are emitted unless explicitly enabled via ECS configuration.

## Enabling EventSource Diagnostics

### Method 1: Environment Variable (Testing/Debugging)

```bash
# Enable for testing
set R9_SUBSTRATE_EVENTSOURCE_ENABLED=true

# Disable (default)
set R9_SUBSTRATE_EVENTSOURCE_ENABLED=false
```

### Method 2: ECS Configuration (Production)

The EventSource can be enabled via ECS configuration for specific services or environments:

```json
{
  "ECSParameters": {
    "DiagnosticsSettings": {
      "SubstrateEventSourceEnabled": true
    }
  }
}
```

## Implementation Details

### EventSource Names
- **Logging**: `Microsoft-M365-Core-Telemetry-R9-Logging-Substrate`
- **Metering**: `Microsoft-M365-Core-Telemetry-R9-Metering-Substrate`

### Control Logic
1. Check environment variable `R9_SUBSTRATE_EVENTSOURCE_ENABLED` first
2. If not set, check ECS configuration
3. Default to **disabled** if neither is configured
4. Configuration is checked once and cached for performance

### Privacy Protection
- **No customer data** is logged in EventSource events
- Only **configuration metadata** and **error information** is captured
- **Service names** and **configuration paths** may be included for diagnostics
- **Connection strings** and **secrets** are never logged

## Usage Examples

### Customer Usage (Default - No Diagnostics)
```csharp
// Standard usage - no diagnostics emitted
services.AddSubstrateLogging(configuration);
services.AddSubstrateMetering(configuration);
```

### Microsoft Internal Usage (Diagnostics Enabled)
```csharp
// Same API - diagnostics controlled by ECS
services.AddSubstrateLogging(configuration);
services.AddSubstrateMetering(configuration);

// EventSource events are emitted when enabled via ECS
```

## Event Categories

When enabled, the following types of events are captured:

### Configuration Issues
- Missing configuration sections
- Invalid configuration values
- Configuration binding failures
- Applied default values

### Initialization Events
- Substrate startup/completion
- Version information
- Configuration issue counts

### Runtime Issues
- Exporter configuration failures
- Enricher registration failures
- Unexpected errors with fallback actions

## Monitoring Setup (When Enabled)

### ETW Collection
```powershell
# Collect substrate diagnostics
logman create trace SubstrateTrace -p "Microsoft-M365-Core-Telemetry-R9-Logging-Substrate" -o substrate.etl
logman start SubstrateTrace
```

### Programmatic Monitoring
```csharp
public class SubstrateEventListener : EventListener
{
    protected override void OnEventSourceCreated(EventSource eventSource)
    {
        if (eventSource.Name.Contains("R9-Logging-Substrate") || 
            eventSource.Name.Contains("R9-Metering-Substrate"))
        {
            EnableEvents(eventSource, EventLevel.Warning);
        }
    }

    protected override void OnEventWritten(EventWrittenEventArgs eventData)
    {
        // Process substrate diagnostic events
        Console.WriteLine($"Substrate: {eventData.EventName} - {eventData.Payload?[0]}");
    }
}
```

## Benefits

### For Customers
- **Zero impact** - No diagnostics overhead by default
- **Privacy protected** - No customer data in diagnostic events
- **Same API** - No changes to existing code
- **Reliable** - Never crashes due to diagnostic failures

### For Microsoft
- **Centralized control** - Enable/disable via ECS
- **Rich diagnostics** - Detailed troubleshooting information
- **Distributed monitoring** - Collect events across services
- **Performance insights** - Track configuration issues at scale

## Migration Path

### Phase 1: Deploy with Diagnostics Disabled (Current)
- All existing APIs work unchanged
- EventSource infrastructure in place but disabled
- Zero customer impact

### Phase 2: Selective Enablement (Future)
- Enable diagnostics for Microsoft internal services
- Monitor and validate diagnostic data quality
- Refine event definitions based on real-world usage

### Phase 3: Broader Enablement (Future)
- Consider enabling for customer services with consent
- Provide customer-facing diagnostic tools
- Integrate with customer monitoring systems

## Security Considerations

- **No secrets logged** - Connection strings, keys, tokens are never captured
- **Metadata only** - Only configuration structure and error types are logged
- **ECS controlled** - Centrally managed enablement reduces security risk
- **Audit trail** - ECS configuration changes are audited
- **Fail-safe** - Diagnostic failures never affect customer service functionality

This approach provides comprehensive diagnostics capabilities while maintaining customer privacy and service reliability as top priorities.
