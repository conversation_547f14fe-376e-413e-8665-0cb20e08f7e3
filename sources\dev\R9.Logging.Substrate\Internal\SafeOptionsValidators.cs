// <copyright file="SafeOptionsValidators.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.Extensions.Options;
using Microsoft.R9.Extensions.Logging;
using Microsoft.R9.Extensions.Logging.Exporters;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Internal
{
    /// <summary>
    /// Safe IValidateOptions implementation for LoggingOptions that doesn't throw exceptions
    /// </summary>
    public class SafeLoggingOptionsValidator : IValidateOptions<LoggingOptions>
    {
        public ValidateOptionsResult Validate(string? name, LoggingOptions options)
        {
            var builder = new ValidateOptionsResultBuilder();

            try
            {
                // Add any specific validation logic for LoggingOptions here
                // For now, LoggingOptions doesn't have strict requirements
                
                return builder.Build();
            }
            catch (Exception ex)
            {
                // Even in validators, we avoid throwing exceptions
                builder.AddError($"Unexpected error during LoggingOptions validation: {ex.Message}");
                return builder.Build();
            }
        }
    }

    /// <summary>
    /// Safe IValidateOptions implementation for GenevaLogExporterOptions that provides fallbacks
    /// </summary>
    public class SafeGenevaLogExporterOptionsValidator : IValidateOptions<GenevaLogExporterOptions>
    {
        public ValidateOptionsResult Validate(string? name, GenevaLogExporterOptions options)
        {
            var builder = new ValidateOptionsResultBuilder();

            try
            {
                var hasErrors = false;

                // Validate ConnectionString
                if (string.IsNullOrEmpty(options.ConnectionString))
                {
                    builder.AddError("ConnectionString is required for Geneva Event. Applying default disabled connection.");
                    options.ConnectionString = "EtwSession=DisabledSession";
                    hasErrors = true;
                }

                // Validate TableNameMappings
                if (options.TableNameMappings == null || options.TableNameMappings.Count == 0)
                {
                    builder.AddError("TableNameMappings is required for Geneva Event. Applying default table mapping.");
                    options.TableNameMappings = new Dictionary<string, string> { ["*"] = "DefaultTable" };
                    hasErrors = true;
                }

                // Note: We apply defaults but still report errors for visibility
                // This allows the system to continue functioning while alerting about configuration issues
                return builder.Build();
            }
            catch (Exception ex)
            {
                // Apply safe defaults even when validation itself fails
                try
                {
                    options.ApplyDefaults();
                }
                catch
                {
                    // If even applying defaults fails, we'll let the framework handle it
                }

                builder.AddError($"Unexpected error during GenevaLogExporterOptions validation: {ex.Message}. Applied default values.");
                return builder.Build();
            }
        }
    }

    /// <summary>
    /// Non-throwing wrapper for IOptions that provides safe access to validated options
    /// </summary>
    /// <typeparam name="T">The options type</typeparam>
    public class SafeOptions<T> where T : class
    {
        private readonly IOptions<T> _options;
        private readonly T _fallbackValue;

        public SafeOptions(IOptions<T> options, T fallbackValue)
        {
            _options = options;
            _fallbackValue = fallbackValue;
        }

        /// <summary>
        /// Gets the options value safely, returning fallback if validation fails
        /// </summary>
        public T Value
        {
            get
            {
                try
                {
                    return _options.Value;
                }
                catch (OptionsValidationException)
                {
                    // Return fallback instead of throwing
                    return _fallbackValue;
                }
                catch
                {
                    // Return fallback for any other errors
                    return _fallbackValue;
                }
            }
        }

        /// <summary>
        /// Tries to get the options value, returning success/failure result
        /// </summary>
        /// <returns>ValidationResult with the options value or errors</returns>
        public ValidationResult<T> TryGetValue()
        {
            try
            {
                var value = _options.Value;
                return ValidationResult<T>.Success(value);
            }
            catch (OptionsValidationException ex)
            {
                var errors = new List<string>();
                foreach (var failure in ex.Failures)
                {
                    errors.Add(failure);
                }
                return ValidationResult<T>.Failure(errors);
            }
            catch (Exception ex)
            {
                return ValidationResult<T>.Failure($"Unexpected error accessing options: {ex.Message}");
            }
        }
    }
}

namespace Microsoft.Extensions.DependencyInjection
{
    /// <summary>
    /// Extension methods for registering safe options validators
    /// </summary>
    public static class SafeOptionsServiceCollectionExtensions
    {
        /// <summary>
        /// Adds safe options validation that applies defaults instead of throwing
        /// </summary>
        public static IServiceCollection AddSafeOptionsValidation<TOptions>(
            this IServiceCollection services,
            TOptions fallbackOptions) 
            where TOptions : class
        {
            // Register the safe wrapper
            services.AddSingleton<SafeOptions<TOptions>>(provider =>
            {
                var options = provider.GetRequiredService<IOptions<TOptions>>();
                return new SafeOptions<TOptions>(options, fallbackOptions);
            });

            return services;
        }
    }
}
