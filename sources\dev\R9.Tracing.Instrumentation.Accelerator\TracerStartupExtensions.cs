﻿// <copyright file="TracerStartupExtensions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator
{
    using System;
#if NETFRAMEWORK
    using System.Threading;
#endif
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
#if NETFRAMEWORK
    using Microsoft.Extensions.Hosting;
    using OpenTelemetry;
    using OpenTelemetry.Context.Propagation;
#endif    
    using Microsoft.M365.Core.Telemetry.ECS.DynamicComponent.Samplers;
    using Microsoft.M365.Core.Telemetry.ECSClient;
    using Microsoft.R9.Extensions.ClusterMetadata.Cosmic;
    using Microsoft.R9.Extensions.Diagnostics;
    using Microsoft.R9.Extensions.Enrichment;
    using Newtonsoft.Json.Linq;
    using OpenTelemetry.Resources;
    using OpenTelemetry.Trace;

    /// <summary>
    ///  For Distributed Tracing
    /// </summary>
    public static class TracerStartupExtensions
    {
        /// <summary>
        /// Cache for build version
        /// </summary>
        internal static string BuildVersionCache = string.Empty;

        /// <summary>
        /// Mainly for Substrate Services, configure Distributed Tracing with settings defined in configuration, default including those components:
        ///     1. ECS controlled Dynamic Sampler
        ///     2. Auto-collect in/out http requests with all tags that might contains EUII redacted
        ///     3. Geneva trace exporter
        ///     4. Necessary enrichers for mandatory information, like env_cloud_role, env_cloud_roleInstance etc.
        ///     5. Customized activity sources
        /// </summary>
        /// <param name="services">Container <see cref="IServiceCollection"/></param>
        /// <param name="serviceMetaDataConfig">Service related informations <see cref="IConfiguration"/></param>
        /// <param name="tracingAcceleratorConfig">Tracing related configurations holder <see cref="IConfiguration"/></param>
        /// <param name="extraTraceProviderBuilderConfigure">Action<TracerProviderBuilder> This is enrichment for special needs. If there are no special needs, please leave this parameter empty. If you really need this parameter, please be sure to understand the default configuration in the interface to avoid configuration conflicts: same component is configured twice, resulting in unexpected behavior. Meanwhile, if you add multiple components, please follow the order: instrumentation => Enrichers => Exporters</param>
        /// <returns>Service Collection</returns>
        public static IServiceCollection AddDistributedTracingService(this IServiceCollection services, IConfiguration serviceMetaDataConfig, IConfiguration tracingAcceleratorConfig = default, Action<TracerProviderBuilder> extraTraceProviderBuilderConfigure = default)
        {
            Throws.IfNull(services, nameof(services));
            Throws.IfNull(serviceMetaDataConfig, nameof(serviceMetaDataConfig));

            var acceleratorOptions = ConfigurationUtility.ParseAndValidateTracingAcceleratorOptions(services, tracingAcceleratorConfig);
            var serviceMetaDataOptions = ConfigurationUtility.ParseAndValidateServiceMetaDataOptions(services, serviceMetaDataConfig);

            var ecsControlledConfig = MockedR9TracingConfig;
            if (ecsControlledConfig is null)
            {
                ecsControlledConfig = ConfigurationUtility.ParseR9TracingConfig(serviceMetaDataOptions, acceleratorOptions.TracingSamplerAndEnabled);
                acceleratorOptions = ConfigurationUtility.RefreshTracingAcceleratorOptions(acceleratorOptions, ecsControlledConfig?.EcsConfigRoot);
            }

            return services.AddDistributedTracingServiceInternal(acceleratorOptions, serviceMetaDataOptions, ecsControlledConfig, extraTraceProviderBuilderConfigure);
        }

#if NETFRAMEWORK
        /// <summary>
        /// Helper funtion to start Services. 
        /// It will start all IHostedService<see cref="IHostedService"/> in the given ServiceCollection.<see cref="IServiceCollection"/>
        /// </summary>
        /// <param name="services">Container used to inject Services.<see cref="IServiceCollection"/></param>
        /// <returns>ServiceProvider<see cref="ServiceProvider"/>built from ServiceCollection</returns>
        public static ServiceProvider StartServices(this IServiceCollection services)
        {
            var serviceProvider = services.BuildServiceProvider();
            var hostedServices = serviceProvider.GetServices<IHostedService>();
            foreach (var hostedService in hostedServices)
            {
                _ = hostedService.StartAsync(CancellationToken.None);
            }
            return serviceProvider;
        }

        /// <summary>
        /// Helper funtion to stop Services/>
        /// It will stop all IHostedService<see cref="IHostedService"/> in the given ServiceCollection.<see cref="IServiceCollection"/>
        /// </summary>
        /// <param name="serviceProvider">Container used to inject Services.<see cref="ServiceProvider"/></param>
        public static void StopServices(this ServiceProvider serviceProvider)
        {
            var hostedServices = serviceProvider.GetServices<IHostedService>();
            foreach (var hostedService in hostedServices)
            {
                _ = hostedService.StopAsync(CancellationToken.None);
            }
        }
#endif

        private static IServiceCollection AddDistributedTracingServiceInternal(this IServiceCollection services, TracingAcceleratorOptions acceleratorOptions, ServiceMetaDataOptions serviceMetaDataOptions, R9TracingConfig ecsControlledConfig, Action<TracerProviderBuilder> extraTraceProviderBuilderConfigure)
        {
            if (!(ecsControlledConfig?.R9DTEnabled ?? false))
            {
                return services;
            }
#if NETFRAMEWORK
            SetDefaultPropagator();
#endif

            // Required for R9 instrumentation lib, mainly for redaction
            services.AddLogging();
            services.AddRedaction();

            /* Do not change the order of the following lines, otherwise the redaction, enricher will not work out as expected
                 1.AddTraceEnricher must be placed before exporters and after other components, otherwise attributes might be overriden by internal logic of R9 instruemntation libs
            */
            Action<TracerProviderBuilder> configure = builder =>
            {
                if (acceleratorOptions.ActivitySources.Length != 0)
                {
                    builder.AddSource(acceleratorOptions.ActivitySources);
                }

                builder
                .SetSampler(new DynamicSampler(ecsControlledConfig))
                .SetResourceBuilder(ResourceBuilder.CreateDefault().AddService(serviceMetaDataOptions.ServiceName, serviceMetaDataOptions.ServiceVersion))
                .AddHttpClientTracing(acceleratorOptions.HttpClientTracing)
                .AddHttpTracing(acceleratorOptions.HttpTracing);
            };

            if (extraTraceProviderBuilderConfigure != default)
            {
                configure += extraTraceProviderBuilderConfigure;
            }

            if (ConfigurationUtility.IsCosmicService(serviceMetaDataOptions.RuntimeModel))
            {
                // Just support parse cosmic attributes from environment instead of configuration file
                services.AddCosmicClusterMetadata(ConfigurationUtility.ConfigureCosmicMetaData);
                configure += builder => builder.AddCosmicTraceEnricher();
            }

            configure += builder =>
            {
                builder
                .AddTraceEnricher<RoleInfoEnricher>()
                .AddTraceEnricher<RedactionComplementEnricher>()
                .AddTraceEnricher<BuildVersionEnricher>()
                .AddGenevaExporter(acceleratorOptions.GenevaTraceExporter, new TraceIdRatioBasedSampler(acceleratorOptions.GenevaTraceExporter.TraceIdBasedSampleRatio));

                if (acceleratorOptions.ODLTraceExporter.IsEnabled)
                {
                    builder.AddODLExporter(acceleratorOptions.ODLTraceExporter);
                }
                if (acceleratorOptions.ODLTcpTraceExporter.IsEnabled)
                {
                    builder.AddODLTcpExporter(acceleratorOptions.ODLTcpTraceExporter);
                }
                if (acceleratorOptions.ConsoleTracingExporter.IsEnabled)
                {
                    builder.AddConsoleExporter(acceleratorOptions.ConsoleTracingExporter);
                }

                builder.SetErrorStatusOnException(true);
            };
            services.AddOpenTelemetry().WithTracing(configure);
            return services;
        }

        /// <summary>
        /// For test purpose; todo:@yangyzh optimize
        /// </summary>
        public static R9TracingConfig MockedR9TracingConfig
        {
            get;
            set;
        }

#if NETFRAMEWORK
        /// <summary>
        /// set default propagator with sdk for .net framework version
        /// </summary>
        internal static void SetDefaultPropagator()
        {
            Sdk.SetDefaultTextMapPropagator(new CompositeTextMapPropagator(new TextMapPropagator[]
            {
                new TraceContextPropagator(),
                new BaggagePropagator(),
            }));
        }
#endif
    }
}